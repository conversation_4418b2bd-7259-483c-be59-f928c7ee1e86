FROM python:3.12-alpine

ARG UID=122
ARG GID=122

ENV UID=${UID} \
    GID=${GID} \
    PYTHONUNBUFFERED=1

RUN apk update && \
    apk add --no-cache bash gcc libc-dev mysql-dev netcat-openbsd pkgconfig shadow && \
    groupadd -g ${GID} appgroup && \
    useradd -u ${UID} -g appgroup -m -o appuser && \
    rm -rf /var/cache/apk/* 

WORKDIR /app

COPY requirements.txt /app/
RUN pip3 install --no-cache-dir -r requirements.txt

COPY . /app/

RUN chown -R appuser:appgroup /app

USER appuser

EXPOSE 8000

