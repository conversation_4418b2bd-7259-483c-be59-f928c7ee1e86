def COLOR_MAP = [
    'SUCCESS': 'good',
    'FAILURE': 'danger',
    'UNSTABLE': 'warning',
    'ABORTED': 'danger'
]

pipeline {
    agent {
        label 'node1'
    }
    
    environment {
        USER = "/home/<USER>"
        DOCKER_IMAGE = 'cobber365/cobber365'
        SONAR_PROJECT_KEY = 'cobber365'
    }
    
    options {
        buildDiscarder(logRotator(numToKeepStr: '2'))
        timeout(time: 2, unit: 'HOURS')
        disableConcurrentBuilds()
    }
    
    stages {
        stage('Dependencies Check') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Verifying installations and dependencies...')
                        sh '''
                            python3 --version
                            pip --version
                            npm --version
                            nodejs --version
                            trivy --version
                            ansible --version
                            docker --version
                        '''
                    } catch (Exception e) {
                        notifySlack('danger', "Dependencies check failed: ${e.message}")
                        throw e
                    }
                }
            }
        }

        stage('Ansible Connectivity Check') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Checking AWS remote host servers')
                        retry(3) {
                            sh 'ansible all -m ping'
                        }
                    } catch (Exception e) {
                        notifySlack('danger', "Ansible connectivity check failed: ${e.message}")
                        throw e
                    }
                }
            }
        }

        stage('Source Code Checkout') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Pulling source code from GitHub')
                        git branch: 'dev',
                            credentialsId: 'github-cred',
                            url: 'https://github.com/Cobber365/cb365-web-development.git'
                    } catch (Exception e) {
                        notifySlack('danger', "Source code checkout failed: ${e.message}")
                        throw e
                    }
                }
            }
        }

        stage('Code Quality Analysis') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Running static code analysis (SonarQube)')
                        def scannerHome = tool 'sonar'
                        withSonarQubeEnv('sonar') {
                            sh """
                                ${scannerHome}/bin/sonar-scanner \
                                    -Dsonar.projectKey=${SONAR_PROJECT_KEY} \
                                    -Dsonar.sourceEncoding=UTF-8
                            """
                        }
                    } catch (Exception e) {
                        notifySlack('danger', "Code quality analysis failed: ${e.message}")
                        throw e
                    }
                }
            }
        }

        stage('Build Gate') {
            steps {
                script {
                    notifySlack('good', 'Build gate awaiting approval')
                    timeout(time: 7, unit: 'DAYS') {
                        input message: 'QA completed, check status before proceeding with docker build'
                    }
                }
            }
        }

        stage('Docker Build') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Building Docker image')
                        sh """
                            docker buildx build \\
                                --platform linux/amd64,linux/arm64 \\
                                -t ${DOCKER_IMAGE} .
                        """
                    } catch (Exception e) {
                        notifySlack('danger', "Docker build failed: ${e.message}")
                        throw e
                    }
                }
            }
        }

        stage('Security Scan') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Running image security scan (Trivy)')
                        sh """
                            trivy --quiet --exit-code 1 \
                                --severity MEDIUM,HIGH,CRITICAL \
                                image ${DOCKER_IMAGE}
                        """
                    } catch (Exception e) {
                        notifySlack('danger', "Security scan failed: ${e.message}")
                        throw e
                    }
                }
            }
        }

        stage('Push Gate') {
            steps {
                script {
                    notifySlack('good', 'Push gate awaiting approval')
                    timeout(time: 7, unit: 'DAYS') {
                        input message: 'Trivy scan completed, push image to registry?'
                    }
                }
            }
        }

        stage('Docker Registry Operations') {
            stages {
                stage('Docker Login') {
                    steps {
                        script {
                            try {
                                notifySlack('good', 'Logging into Docker registry')
                                withCredentials([
                                    usernamePassword(
                                        credentialsId: 'docker-cred',
                                        passwordVariable: 'DOCKER_PASSWORD',
                                        usernameVariable: 'DOCKER_USERNAME'
                                    )
                                ]) {
                                    sh '''
                                        echo $DOCKER_PASSWORD | \
                                        docker login -u $DOCKER_USERNAME --password-stdin
                                    '''
                                }
                            } catch (Exception e) {
                                notifySlack('danger', "Docker login failed: ${e.message}")
                                throw e
                            }
                        }
                    }
                }

                stage('Push Image') {
                    steps {
                        script {
                            try {
                                notifySlack('good', 'Pushing Docker image')
                                retry(3) {
                                    sh "docker push ${DOCKER_IMAGE}"
                                }
                            } catch (Exception e) {
                                notifySlack('danger', "Image push failed: ${e.message}")
                                throw e
                            }
                        }
                    }
                }

                stage('Docker Logout') {
                    steps {
                        script {
                            sh 'docker logout'
                        }
                    }
                }
            }
        }

        stage('Deployment Gate') {
            steps {
                script {
                    notifySlack('good', 'Deployment gate awaiting approval')
                    timeout(time: 7, unit: 'DAYS') {
                        input message: 'Build stage completed, deploy to Staging/Production?'
                    }
                }
            }
        }

        stage('Deploy Application') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Starting deployment with Ansible')
                        withCredentials([
                            string(credentialsId: 'vault_key', variable: 'VAULT_KEY')
                        ]) {
                            writeFile file: '/tmp/vault_key.txt', text: VAULT_KEY
                            sh """
                                ansible-playbook \
                                    --vault-password-file=/tmp/vault_key.txt \
                                    -e @${USER}/ansible/vault_secrets.yaml \
                                    ansible-deployment.yaml
                            """
                            sh 'rm -f /tmp/vault_key.txt'
                        }
                    } catch (Exception e) {
                        notifySlack('danger', "Deployment failed: ${e.message}")
                        throw e
                    }
                }
            }
        }

        stage('Final Verification') {
            steps {
                script {
                    notifySlack('good', 'Final verification gate')
                    timeout(time: 7, unit: 'DAYS') {
                        input message: 'Confirm if deployment was successful before cleanup'
                    }
                }
            }
        }

        stage('Cleanup') {
            steps {
                script {
                    try {
                        notifySlack('good', 'Removing secrets from app server')
                        sh 'ansible-playbook ansible-deployment-2.yaml'
                    } catch (Exception e) {
                        notifySlack('danger', "Cleanup failed: ${e.message}")
                        throw e
                    }
                }
            }
        }
    }

    post {
        always {
            script {
                notifySlack(
                    COLOR_MAP[currentBuild.currentResult],
                    """
                    *${currentBuild.currentResult}:* Job ${env.JOB_NAME} build ${env.BUILD_NUMBER} error ${env.BUILD_LOG}
                    More info at: ${env.BUILD_URL}
                    """
                )
            }
        }
    }
}

// Utility function for Slack notifications
def notifySlack(color, message) {
    try {
        slackSend(
            channel: "#cb365-jenkins-cicd",
            color: color,
            message: message,
            tokenCredentialId: 'slack-cred'
        )
    } catch (Exception e) {
        echo "Failed to send Slack notification: ${e.message}"
        // Continue pipeline execution even if Slack notification fails
    }
}
