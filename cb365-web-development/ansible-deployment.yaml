---
#Deploying Application and mysql database to my private server
- hosts: private
  gather_facts: yes
  become: true
  vars:
    vars_files_var:
      # vault_secrets.yaml should be located on the ansible host machine at /home/<USER>/ansible/vault_secrets.yaml
      - "{{ lookup('env', 'USER') }}/ansible/vault_secrets.yaml"
  tasks:
    - name: Load secrets from vault_secrets.yaml
      include_vars:
        file: "{{ item }}"
      with_items: "{{ vars_files_var }}"

    #This is how secrets should be store in vault_secrets.yaml
    # github_user: "value"
    # github_token: "value"
    # docker_user: "value"
    # docker_token: "value"
    # SECRET_KEY: "value"
    # EMAIL_HOST_USER:  "value"
    # EMAIL_HOST_PASSWORD:  "value"
    # MYSQL_DATABASE: "value"
    # MYSQL_USER: "value"
    # MYSQL_PASSWORD:  "value"
    # MYSQL_ROOT_PASSWORD:  "value"
    # SUPERUSER_USERNAME = "value"
    # SUPERUSER_EMAIL = "value"
    # SUPERUSER_PASSWORD = "value"

    - name: "Update Server"
      apt:
        update_cache: yes

    - name: "Installing git if not present"
      apt:
        name: git
        state: present

    - name: "Checking if /mnt/app if present"
      file:
        path: "/mnt/app"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        recurse: yes

    - name: "Git Checkout"
      ansible.builtin.git:
        repo: https://{{ github_user }}:{{ github_token }}@github.com/Cobber365/cb365-web-development.git
        dest: "/mnt/app/"
        version: dev
        update: yes
        force: yes
      no_log: false

    - name: "Create mount bind for mysql container"
      file:
        path: "/mnt/mysql/"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: 0755
        recurse: yes

    - name: "Update Server"
      apt:
        update_cache: yes

    - name: "Installing nfs-common"
      apt:
        name: nfs-common

    - name: "Mounting efs on nginx dir"
      shell: "sudo mount -t nfs4 -o nfsvers=4.1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport fs-0080788f189b500b7.efs.eu-west-1.amazonaws.com:/ /mnt/app/nginx/"

    - name: "Creating media dir"
      file:
        path: "/mnt/app/nginx/media/"
        state: directory

    - name: "Change ownership of app directory and files"
      ansible.builtin.file:
        path: "/mnt/"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: 0755
        recurse: yes

    - name: "Copying secrets to env secrets"
      copy:
        dest: "/mnt/app/.env"
        content: |
          SECRET_KEY="{{SECRET_KEY}}"
          DEBUG ="{{DEBUG}}"
          ALLOWED_HOSTS="{{ALLOWED_HOSTS}}"
          EMAIL_HOST_USER= "{{EMAIL_HOST_USER}}"
          EMAIL_HOST_PASSWORD= "{{EMAIL_HOST_PASSWORD}}"
          MYSQL_HOST="{{MYSQL_HOST}}"
          MYSQL_DATABASE= "{{MYSQL_DATABASE}}"
          MYSQL_USER= "{{MYSQL_USER}}"
          MYSQL_PASSWORD= "{{MYSQL_PASSWORD}}"
          MYSQL_ROOT_PASSWORD= "{{MYSQL_ROOT_PASSWORD}}"
          SUPERUSER_USERNAME = "{{SUPERUSER_USERNAME}}"
          SUPERUSER_EMAIL = "{{SUPERUSER_EMAIL}}"
          SUPERUSER_PASSWORD = "{{SUPERUSER_PASSWORD}}"
        mode: "0644"
      no_log: true

    - name: "Enabling read mode"
      shell: "chmod +r /mnt/app/nginx/"

    - name: "Setting uid & gid for app dir"
      shell: "chown -R 122:122 /mnt/app/"

    - name: "Logging in docker"
      docker_login:
        username: "{{ docker_user }}"
        password: "{{ docker_token }}"
      no_log: true

    - name: "Running docker compose"
      shell: "cd /mnt/app/ && docker compose up -d"

    - name: "Setting uid & gid for app dir"
      shell: "chown -R 122:122 /mnt/app/nginx"

    - name: "Run collectstatic command inside the container"
      community.docker.docker_container_exec:
        container: cb365_app
        command: "python manage.py collectstatic --noinput"
        detach: false

    - name: "Checking if app & mysql containers are running"
      shell: "docker ps"

    - name: "Logging out docker"
      docker_login:
        state: absent

- hosts: bastion
  gather_facts: yes
  become: yes
  tasks:
    - name: "Update Server"
      apt:
        update_cache: yes

    - name: "Installing nfs-common"
      apt:
        name: nfs-common

    - name: "Creating efs dir"
      file:
        path: "/var/www/html/nginx/"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: 0755
        recurse: yes

    - name: "Add read mode to efs"
      shell: "chmod +r /var/www/html/nginx"

    - name: "Mount efs"
      shell: "sudo mount -t nfs4 -o nfsvers=4.1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport fs-0080788f189b500b7.efs.eu-west-1.amazonaws.com:/ /var/www/html/nginx"

    - name: "Deleting certbot.lock"
      file:
        path: "/var/log/letsencrypt/.certbot.lock"
        state: absent

    - name: "Deleting letsencrypt.log"
      file:
        path: "/var/log/letsencrypt/letsencrypt.log"
        state: absent

    - name: "Delete certbot log"
      file:
        path: "/tmp/certbot-log-4sqv6ue7/log"
        state: absent

    - name: "Installing nginx and other pre-requisites"
      apt:
        name: nginx
        state: present
        update_cache: yes

    - name: "Update server"
      apt:
        update_cache: yes

    - name: "Enabling nginx"
      service:
        name: nginx
        enabled: yes

    - name: "Create upstream"
      copy:
        dest: /etc/nginx/conf.d/upstreams.conf
        content: |
          upstream backend {
              server 10.0.1.244:2023;
          }

    - name: "Configuring nginx listener and reverse proxy"
      copy:
        dest: /etc/nginx/nginx.conf
        content: |
          user www-data;
          worker_processes 1;
          pid /run/nginx.pid;
          include /etc/nginx/modules-enabled/*.conf;

          events {
            worker_connections 1024;
          }

          http {
            include /etc/nginx/mime.types;
            include /etc/nginx/conf.d/*.conf;
            include /etc/nginx/sites-enabled/*;

            client_max_body_size 20M;
            sendfile on;
            tcp_nopush on;
            tcp_nodelay on;
            keepalive_timeout 65;
            types_hash_max_size 2048;
            server_tokens off;
          }

    - name: "Create site configuration"
      copy:
        dest: /etc/nginx/sites-available/cobber365.com
        content: |
          # HTTP server block for redirection to HTTPS
            server {
              listen 80;
              server_name cobber365.com www.cobber365.com;
              return 301 https://$host$request_uri;
            }

            # HTTPS server block (Certbot will modify this block)
            server {
              #ssl certbot directives will be appended here



              add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
              add_header X-Content-Type-Options nosniff;
              add_header X-Frame-Options SAMEORIGIN;

              # Static files location
              location /static/ {
                alias /var/www/html/nginx/staticfiles/;
                expires off;
                access_log off;
              }

              # Media files location
              location /media/ {
                alias /var/www/html/nginx/media/;
                expires off;
                access_log off;
              }

              # Main proxy to Django application
              location / {
                proxy_pass http://backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Host $host;
                
                proxy_connect_timeout 75s;
                proxy_send_timeout 3600s;
                proxy_read_timeout 3600s;
                send_timeout 3600s;
                
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
              }
            }

    - name: "Installing snap package"
      apt:
        name: snapd
        update_cache: yes

    - name: "Installing certbot --classic"
      shell: snap install certbot --classic

    - name: "Installing core"
      shell: snap install core

    - name: "Refreshing core"
      shell: snap refresh core

    - name: "Remove certbot from snap dir"
      file:
        path: /usr/bin/certbot
        state: absent

    - name: "Making snap globally accessible"
      shell: ln -s /snap/bin/certbot /usr/bin/certbot

    - name: "Obtain SSL certificate with certbot certonly"
      command: >
        certbot certonly --nginx -d cobber365.com -d www.cobber365.com
                        --agree-tos
                        --non-interactive
                        --email <EMAIL>
      register: certbot_output

    - name: "Show certbot output for debugging"
      debug:
        var: certbot_output

    - name: "Append SSL directives to nginx.conf"
      blockinfile:
        path: /etc/nginx/sites-available/cobber365.com
        insertafter: "#ssl certbot directives will be appended here"
        block: |
          listen 443 ssl;
          server_name cobber365.com www.cobber365.com;
          ssl_certificate /etc/letsencrypt/live/cobber365.com/fullchain.pem;
          ssl_certificate_key /etc/letsencrypt/live/cobber365.com/privkey.pem;
          include /etc/letsencrypt/options-ssl-nginx.conf;
          ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    - name: "Enable site"
      file:
        src: /etc/nginx/sites-available/cobber365.com
        dest: /etc/nginx/sites-enabled/cobber365.com
        state: link

    - name: "Checking if certificate is set up correctly"
      shell: certbot renew --dry-run

    - name: "Testing nginx conf"
      shell: nginx -t

    - name: "Restart nginx"
      service:
        name: nginx
        state: restarted
