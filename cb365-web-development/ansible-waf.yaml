---
- name: Deploy Enterprise-Grade WAF with CrowdSec and ModSecurity
  hosts: bastion
  become: yes
  vars:
    # Version Configuration
    crs_version: "4.13.0"

    # Path Configuration
    crs_path: "/etc/nginx/modsec-crs/coreruleset-{{ crs_version }}"
    modsec_conf: "/etc/modsecurity/modsecurity.conf"
    nginx_conf: "/etc/nginx/nginx.conf"
    sites_available: "/etc/nginx/sites-available/cobber365.com"

    # Security Parameters
    permanent_bantime: 63072000 # 2 years in seconds
    temp_bantime: 86400 # 24 hours for normal violations

    # IP Blocklists (can be extended)
    bad_user_agents:
      - "nmap"
      - "sqlmap"
      - "nikto"
      - "metasploit"

    # Whitelisted IP addresses
    whitelisted_ips:
      - "**************"
      - "**************"

  tasks:
    # ============================================
    # PHASE 1: MODSECURITY DEPLOYMENT
    # ============================================
    - name: "Install ModSecurity Components"
      block:
        - name: "Update package index"
          apt:
            update_cache: yes
            cache_valid_time: 3600

        - name: "Install ModSecurity for Nginx"
          apt:
            name: "libnginx-mod-security"
            state: latest

        - name: "Verify default config exists"
          stat:
            path: "/etc/modsecurity/modsecurity.conf-recommended"
          register: modsec_default_conf

        - name: "Deploy ModSecurity config"
          copy:
            src: "/etc/modsecurity/modsecurity.conf-recommended"
            dest: "{{ modsec_conf }}"
            owner: root
            group: root
            mode: "0640"
          when: modsec_default_conf.stat.exists

    # ============================================
    # PHASE 2: MODSECURITY HARDENING
    # ============================================
    - name: "Configure Core ModSecurity Settings"
      blockinfile:
        path: "{{ modsec_conf }}"
        marker: "# {mark} ANSIBLE MANAGED - CORE SETTINGS"
        block: |
          SecRuleEngine DetectionOnly
          SecAuditLog /var/log/nginx/modsec_audit.log
          SecAuditLogType Serial
          SecAuditLogParts ABCEFHJKZ
          SecAuditLogRelevantStatus "^(?:5|4(?!04))"
          SecDebugLog /var/log/nginx/modsec_debug.log
          SecDebugLogLevel 3

    - name: "Create secure log directories"
      file:
        path: "{{ item }}"
        state: directory
        owner: www-data
        group: www-data
        mode: "0750"
      loop:
        - /var/log/nginx/modsec_audit/
        - /var/log/nginx/modsec_debug/

    # ============================================
    # PHASE 3: OWASP CRS DEPLOYMENT
    # ============================================
    - name: "Deploy OWASP Core Rule Set"
      block:
        - name: "Create CRS directory structure"
          file:
            path: "/etc/nginx/modsec-crs"
            state: directory
            mode: "0750"

        - name: "Download CRS (validate checksum)"
          get_url:
            url: "https://github.com/coreruleset/coreruleset/archive/refs/tags/v{{ crs_version }}.tar.gz"
            dest: "/tmp/owasp-crs.tar.gz"
            checksum: "sha256:690a92509c60fdda886ac62f5bb01bb77ae480b8eb2c241d687839f27b36062a" # Verify latest checksum!

        - name: "Extract CRS rules"
          unarchive:
            src: "/tmp/owasp-crs.tar.gz"
            dest: "/etc/nginx/modsec-crs"
            remote_src: yes
            extra_opts: ["--strip-components=1"]

        - name: "Configure CRS inclusions"
          blockinfile:
            path: "{{ modsec_conf }}"
            marker: "# {mark} ANSIBLE MANAGED - CRS RULES"
            block: |
              Include {{ crs_path }}/crs-setup.conf
              Include {{ crs_path }}/rules/REQUEST-900-EXCLUSION-RULES-BEFORE-CRS.conf
              Include {{ crs_path }}/rules/REQUEST-901-INITIALIZATION.conf
              Include {{ crs_path }}/rules/*.conf
              Include {{ crs_path }}/rules/RESPONSE-999-EXCLUSION-RULES-AFTER-CRS.conf

    # ============================================
    # PHASE 4: PERMANENT BAN RULES
    # ===========================================
    - name: "Implement Permanent Ban Rules"
      blockinfile:
        path: "{{ modsec_conf }}"
        marker: "# {mark} ANSIBLE MANAGED - PERMABAN RULES"
        block: |
          # Critical Attack Patterns
          SecRule REQUEST_METHOD "@pm POST PUT" \
            "id:1000,phase:1,deny,status:403,\
            msg:'Dangerous HTTP method',\
            setvar:ip.permanent_ban=1"

          SecRule ARGS "@rx <script>|javascript:" \
            "id:1001,phase:2,deny,status:403,\
            msg:'XSS Attempt',\
            setvar:ip.permanent_ban=1"

          SecRule ARGS "@rx (?:union.*select|sleep\(\d+\))" \
            "id:1002,phase:2,deny,status:403,\
            msg:'SQLi Attempt',\
            setvar:ip.permanent_ban=1"

          # Bad User Agents
          SecRule REQUEST_HEADERS:User-Agent "@pmFromFile {{ crs_path }}/rules/REQUEST-913-SCANNER-DETECTION.data" \
            "id:1003,phase:1,deny,status:403,\
            msg:'Malicious Scanner Detected',\
            setvar:ip.permanent_ban=1"

    # ============================================
    # PHASE 5: CROWDSEC INTEGRATION
    # ============================================
    - name: "Install CrowdSec"
      block:
        - name: "Install CrowdSec"
          apt:
            name: crowdsec
            state: latest

        - name: "Install CrowdSec Nginx bouncer"
          apt:
            name: crowdsec-nginx
            state: latest

        - name: "Start and enable CrowdSec service"
          service:
            name: crowdsec
            state: started
            enabled: yes

    # ============================================
    # PHASE 6: NGINX RATE LIMITING
    # ============================================
    - name: "Configure Nginx Rate Limiting"
      blockinfile:
        path: "{{ nginx_conf }}"
        insertafter: "^http {"
        marker: "# {mark} ANSIBLE MANAGED - NGINX RATE LIMITING"
        block: |
          limit_req_zone $binary_remote_addr zone=req_limit_per_ip:10m rate=10r/s;
          limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

          server {
            listen 80;
            location / {
              limit_req zone=req_limit_per_ip burst=20 nodelay;
              limit_conn conn_limit_per_ip 10;
            }
          }

    # ============================================
    # PHASE 7: IPSet + IPTables Bans
    # ============================================
    - name: "Create permanent ban ipset"
      command: |
        ipset create permanent_ban hash:ip timeout {{ permanent_bantime }} || true
        iptables -I INPUT -m set --match-set permanent_ban src -j DROP || true
      args:
        creates: "/proc/net/ipset/permanent_ban"

    - name: "Whitelisted IPs for ModSecurity and CrowdSec"
      blockinfile:
        path: "{{ modsec_conf }}"
        marker: "# {mark} ANSIBLE MANAGED - WHITELISTED IPs"
        block: |
          SecRule REMOTE_ADDR "@ipMatch {{ whitelisted_ips | join(',') }}" \
            "phase:1,allow,ctl:ruleEngine=Off"

    # ============================================
    # PHASE 8: RESTART SERVICES
    # ============================================
    - name: "Restart services"
      service:
        name: "{{ item }}"
        state: restarted
        enabled: yes
      loop:
        - nginx
        - crowdsec
        - fail2ban

    # ============================================
    # PHASE 9: VALIDATION
    # ============================================
    - name: "Run security tests"
      block:
        - name: "Test ModSecurity blocking"
          uri:
            url: "http://localhost/?test=1'%20OR%201=1--"
            status_code: 403
            timeout: 5
          register: modsec_test
          ignore_errors: yes

        - name: "Verify ModSecurity response"
          assert:
            that: modsec_test.status == 403
            msg: "ModSecurity failed to block SQLi test"

        - name: "Test CrowdSec Banning"
          command: "crowdsec -l"
          register: crowdsec_status
          changed_when: false

        - name: "Verify CrowdSec Bans"
          debug:
            msg: "CrowdSec currently banning IPs: {{ crowdsec_status.stdout_lines }}"
