from django.contrib import admin
from . import models
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import CKEditor5Widget
from django.utils.html import format_html


# Register your models here.

# --- Profile Admin Start -----
class ProjectMessageInline(admin.StackedInline):
    model = models.ProjectMessage
    max_num = 1

class ProfileAdmin(admin.ModelAdmin):

    fieldsets = [
        ('General', {'fields': ('title', 'favicon', 'logo')}),
        ('Social Handles', {'fields': ('linkedin', 'x', 'facebook', 'instagram')})
    ]
    inlines = [ProjectMessageInline]
    
    # Prevent adding new Profile instances
    def has_add_permission(self, request):
        # Only allow adding a Profile if there are no existing ones
        return models.Profile.objects.count() == 0

    # Prevent deleting the existing Profile
    def has_delete_permission(self, request, obj=None):
        return False  # Disable delete option

    # Only allow editing the first (and only) profile
    def get_queryset(self, request):
        # Only return the profile with id=1, or an empty queryset if none exists
        queryset = super().get_queryset(request)
        return queryset.filter(id=1)  # Only the profile with id=1, or an empty queryset if none exists
    



# -- AboutUs Admin ------
class OurValuesInline(admin.TabularInline):
    model = models.OurValues
    extra = 0
    max_num = 4


class AboutUsAdmin(admin.ModelAdmin):
    fieldsets = [
        ('General', {'fields': ('breadcrumbs_image','title')}),
        ('About Us', {'fields': ('about', 'about_image', 'read_more')}),
        ('Our Mission', {'fields': ('mission', 'mission_image')}),
        ('Partnership', {'fields': ('partnership', 'partnership_image', 'link')})
    ]
    inlines = [OurValuesInline]
    
    def truncated_about(self, obj):
        # Check if 'about' is None to avoid errors
        if obj.about:
            return format_html('<span>{}</span>', obj.about[:100] + '...' if len(obj.about) > 100 else obj.about)
        return ''  
    truncated_about.short_description = 'About Us'

    def has_add_permission(self, request):
        return models.AboutUs.objects.count()  == 0



# --- Team Admin Start -----
class ChildInline(admin.StackedInline):
    model = models.TeamMembers
    extra = 0  

class TeamAdmin(admin.ModelAdmin):
    list_display = ('title',)  
    inlines = [ChildInline]  
    list_display_links = ('title',)  



# -- Career Admin -----
class JobApplicationInline(admin.StackedInline):
    model = models.JobApplication
    extra = 0

class CareersAdmin(admin.ModelAdmin):
    list_display = ('title', 'location')
    inlines = [JobApplicationInline]


#---Contact Admin-----
class AddressInline(admin.StackedInline):
    model = models.ContactAddress
    extra = 0

class ContactAdmin(admin.ModelAdmin):
    list_display = ('title', 'image')
    inlines = [AddressInline]

    def has_add_permission(self, request):
        return models.Contact.objects.count()  == 0







admin.site.register(models.Profile, ProfileAdmin)
admin.site.register(models.AboutUs, AboutUsAdmin)
admin.site.register(models.Team, TeamAdmin)
admin.site.register(models.Careers, CareersAdmin)
admin.site.register(models.Contact, ContactAdmin)
