from . import models


#Define context
def global_view(request):
    
    profile = models.Profile.objects.all()
    project_message = models.ProjectMessage.objects.all()
    about_us = models.AboutUs.objects.all()
    careers = models.Careers.objects.all()
    job_application = models.JobApplication.objects.all()
    our_team = models.Team.objects.all()
    team_members = models.TeamMembers.objects.all()
    homepage_about_us = models.AboutUs.objects.first()
    contact = models.Contact.objects.all()


    #Plural names
    about_us_plural_name = models.AboutUs._meta.verbose_name_plural
    team_plural_name = models.Team._meta.verbose_name_plural
    careers_plural_name = models.Careers._meta.verbose_name_plural
    contact_plural_name = models.Contact._meta.verbose_name_plural


    return {
        'homepage_about_us': homepage_about_us,
        'careers': careers,
        'job_application': job_application,
        'about_us': about_us,
        'profile': profile,
        'project_message': project_message,
        'our_team': our_team,
        'team_members': team_members,
        'about_us_plural_name': about_us_plural_name,
        'team_plural_name': team_plural_name,
        'careers_plural_name': careers_plural_name,
        'contact_plural_name': contact_plural_name,
        'contact': contact,
    }