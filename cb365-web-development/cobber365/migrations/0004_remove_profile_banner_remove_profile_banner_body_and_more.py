# Generated by Django 5.1 on 2025-01-04 10:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cobber365', '0003_alter_profile_facebook'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='profile',
            name='banner',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='banner_body',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='banner_heading',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='button',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='video_banner',
        ),
        migrations.AlterField(
            model_name='profile',
            name='address1',
            field=models.TextField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='address2',
            field=models.TextField(blank=True, max_length=250, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='profile',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
    ]
