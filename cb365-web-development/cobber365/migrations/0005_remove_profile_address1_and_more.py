# Generated by Django 5.1 on 2025-01-04 13:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cobber365', '0004_remove_profile_banner_remove_profile_banner_body_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='profile',
            name='address1',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='address1_location',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='address2',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='address2_location',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='email',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='office_hours1',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='office_hours2',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='phone1',
        ),
        migrations.Remove<PERSON>ield(
            model_name='profile',
            name='phone2',
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country', models.CharField(max_length=100)),
                ('address', models.TextField(blank=True, max_length=250)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='profile_address', to='cobber365.profile')),
            ],
            options={
                'verbose_name_plural': 'Address(s)',
            },
        ),
    ]
