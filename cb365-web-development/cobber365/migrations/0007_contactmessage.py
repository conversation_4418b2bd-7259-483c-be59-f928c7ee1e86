# Generated by Django 5.1 on 2025-01-04 14:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cobber365', '0006_alter_address_profile'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('contact_message', models.TextField(blank=True)),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contact_message', to='cobber365.profile')),
            ],
            options={
                'verbose_name_plural': 'Contact Message',
            },
        ),
    ]
