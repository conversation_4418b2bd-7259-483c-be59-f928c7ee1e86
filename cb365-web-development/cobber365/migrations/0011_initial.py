# Generated by Django 5.1 on 2025-01-20 18:41

import django.db.models.deletion
import django_ckeditor_5.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cobber365', '0010_remove_aboutusextras_pages_remove_address_profile_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AboutUs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('about_image', models.ImageField(blank=True, null=True, upload_to='about-us/')),
                ('about', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='About')),
                ('mission_image', models.ImageField(blank=True, null=True, upload_to='about-us/')),
                ('mission', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Mission')),
                ('partnership', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Partnership')),
                ('link', models.CharField(blank=True, max_length=50, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('slug', models.SlugField(blank=True, null=True, unique=True)),
            ],
            options={
                'verbose_name_plural': 'About Us',
            },
        ),
        migrations.CreateModel(
            name='Careers',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250, verbose_name='Job')),
                ('location', models.CharField(blank=True, max_length=50, null=True, verbose_name='Location')),
            ],
            options={
                'verbose_name_plural': 'Careers',
            },
        ),
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('favicon', models.ImageField(blank=True, null=True, upload_to='favicon')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logo/')),
                ('facebook', models.URLField(blank=True, null=True)),
                ('instagram', models.URLField(blank=True, null=True)),
                ('linkedin', models.URLField(blank=True, null=True)),
                ('x', models.URLField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Company Profile',
            },
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('body', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body')),
                ('slug', models.SlugField(blank=True, null=True, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Our Team',
            },
        ),
        migrations.CreateModel(
            name='AboutUsExtras',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=100, null=True)),
                ('extras', models.TextField(blank=True, max_length=250, null=True)),
                ('pages', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='extras', to='cobber365.aboutus')),
            ],
            options={
                'verbose_name_plural': 'Extras',
            },
        ),
        migrations.CreateModel(
            name='JobApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_summary', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Job Summary')),
                ('gender', models.CharField(choices=[('Male', 'Male'), ('Female', 'Female'), ('Others', 'Others')], max_length=50)),
                ('first_name', models.CharField(max_length=250)),
                ('middle_name', models.CharField(blank=True, max_length=250, null=True)),
                ('last_name', models.CharField(max_length=250)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(max_length=20, verbose_name='Phone Number')),
                ('address', models.TextField()),
                ('resume_cv', models.FileField(upload_to='careers/', verbose_name='Resume / CV')),
                ('cover_letter', models.TextField()),
                ('job_type', models.CharField(choices=[('Yes', 'Yes'), ('No', 'No')], max_length=50)),
                ('linkedin', models.URLField(blank=True, null=True)),
                ('github', models.URLField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('careers', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_application', to='cobber365.careers')),
            ],
            options={
                'verbose_name_plural': 'Job Applications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OurValues',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=100, null=True)),
                ('body', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body')),
                ('pages', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='our_values', to='cobber365.aboutus')),
            ],
            options={
                'verbose_name_plural': 'Our Values',
            },
        ),
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('contact_message', models.TextField(blank=True)),
                ('svg_animation', models.CharField(blank=True, max_length=500, null=True)),
                ('show_svg_animation', models.BooleanField(default=False)),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contact_message', to='cobber365.profile')),
            ],
            options={
                'verbose_name_plural': 'Contact Message',
            },
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country', models.CharField(max_length=100)),
                ('address', models.TextField(blank=True, max_length=250)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='address', to='cobber365.profile')),
            ],
            options={
                'verbose_name_plural': 'Address(s)',
            },
        ),
        migrations.CreateModel(
            name='TeamMembers',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='ourteam/')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.CharField(max_length=250)),
                ('is_active', models.BooleanField(default=True)),
                ('team', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='team_members', to='cobber365.team')),
            ],
            options={
                'verbose_name_plural': 'Team Members',
            },
        ),
    ]
