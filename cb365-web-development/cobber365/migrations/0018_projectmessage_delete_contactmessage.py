# Generated by Django 5.1 on 2025-03-21 08:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("cobber365", "0017_rename_image_team_breadcrumbs_image_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProjectMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("project_message", models.TextField(blank=True)),
                (
                    "profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="project_message",
                        to="cobber365.profile",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Discuss Project Message",
            },
        ),
        migrations.DeleteModel(
            name="ContactMessage",
        ),
    ]
