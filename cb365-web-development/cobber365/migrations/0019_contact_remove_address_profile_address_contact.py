# Generated by Django 5.1 on 2025-03-21 10:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("cobber365", "0018_projectmessage_delete_contactmessage"),
    ]

    operations = [
        migrations.CreateModel(
            name="Contact",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                ("image", models.ImageField(blank=True, upload_to="contact/")),
                ("contact_message", models.TextField(blank=True, max_length=250)),
            ],
            options={
                "verbose_name_plural": "Contact Us",
            },
        ),
        migrations.RemoveField(
            model_name="address",
            name="profile",
        ),
        migrations.AddField(
            model_name="address",
            name="contact",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="address",
                to="cobber365.contact",
            ),
        ),
    ]
