# Generated by Django 5.1 on 2025-03-21 15:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("cobber365", "0019_contact_remove_address_profile_address_contact"),
    ]

    operations = [
        migrations.CreateModel(
            name="ContactAddress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("country", models.Char<PERSON>ield(max_length=100)),
                ("address", models.TextField(blank=True, max_length=250)),
                ("phone", models.<PERSON>r<PERSON>ield(blank=True, max_length=20)),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                (
                    "contact",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contact_address",
                        to="cobber365.contact",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Address(s)",
            },
        ),
        migrations.DeleteModel(
            name="Address",
        ),
    ]
