from django.db import models
from django.utils.html import mark_safe
from django.core.exceptions import ValidationError
import re
from PIL import Image
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

# Create your models here.

IMG_TAG_FORMAT = '<img src="%s" />'

# --- Profile Model Start ---
class Profile(models.Model):
    title = models.CharField(max_length=100)
    favicon = models.ImageField(upload_to="favicon", null=True, blank=True)
    logo = models.ImageField(upload_to='logo/', null=True, blank=True)
    facebook = models.URLField(max_length=200, blank=True, null=True)
    instagram = models.URLField(max_length=200, blank=True, null=True)
    linkedin = models.URLField(max_length=200, blank=True, null=True)
    x = models.URLField(max_length=200, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Company Profile"

    def logo_image(self):
        if self.logo:
            return mark_safe(IMG_TAG_FORMAT % (self.logo.url))
        else:
            return ""
            
    def __str__(self):
        return self.title

        
class ProjectMessage(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name="project_message")
    project_message = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = "Discuss Project Message"

    def __str__(self):
        return self.project_message
    


#---Contact us-----
class Contact(models.Model):
    title = models.CharField(max_length=100)
    breadcrumbs_image = models.ImageField(upload_to='contact/', blank=True)
    image = models.ImageField(upload_to='contact/', blank=True)
    
    class Meta:
        verbose_name_plural = "Contact Us"

    def contact_image(self):
        if self.image:
            return mark_safe(IMG_TAG_FORMAT % (self.image.url))
        else:
            return ""
        
    def breadcrumbs(self):
        return mark_safe(IMG_TAG_FORMAT % (self.breadcrumbs_image.url))
        
    def __str__(self):
        return self.title

class ContactAddress(models.Model):
    contact = models.ForeignKey(Contact, on_delete=models.CASCADE, related_name="contact_address", null=True)
    country = models.CharField(max_length=100)
    address = models.TextField(max_length=250, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(max_length=254, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Address(s)"

    def clean(self):
        if self.phone and not re.match(r'^\+?\d{10,15}$', self.phone):
            raise ValidationError({'phone': 'Invalid phone number format.'})
    


ABOUT_US = "about-us/"
# -- About Us ----
class AboutUs(models.Model):
    breadcrumbs_image = models.ImageField(upload_to=ABOUT_US, null=True, blank=True)
    title = models.CharField(max_length=250)
    about_image = models.ImageField(upload_to=ABOUT_US, blank=True, null=True)
    about = CKEditor5Field('AboutUs', config_name='extends', blank=True, null=True)
    read_more = models.CharField(max_length=50, null=True, blank=True)
    mission_image = models.ImageField(upload_to=ABOUT_US, blank=True, null=True)
    mission = CKEditor5Field('Mission', config_name='extends', blank=True, null=True)
    partnership_image = models.ImageField(upload_to=ABOUT_US, blank=True, null=True)
    partnership = CKEditor5Field('Partnership', config_name='extends', blank=True, null=True)
    link = models.CharField(max_length=50, blank=True, null=True)
    slug = models.SlugField(unique=True, blank=True, null=True)

    class Meta:
        verbose_name_plural = 'About Us'

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    def breadcrumbs(self):
        return mark_safe(IMG_TAG_FORMAT % (self.breadcrumbs_image.url))

    
class OurValues(models.Model):
    pages = models.ForeignKey(AboutUs, related_name="our_values", on_delete=models.CASCADE)
    title = models.CharField(max_length=100, null=True)
    values = models.TextField(max_length=250, null=True)

    class Meta:
        verbose_name_plural = "Our Values"

    def __str__(self):
        return self.title
    


# Team and Child Model
OUR_TEAM = "ourteam/"
class Team(models.Model):
    breadcrumbs_image = models.ImageField(upload_to=OUR_TEAM, null=True, blank=True)
    title = models.CharField(max_length=100)
    text = CKEditor5Field('Body', config_name='extends', blank=True, null=True)
    slug = models.SlugField(unique=True, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Our Team"

    def breadcrumbs(self):
        return mark_safe(IMG_TAG_FORMAT % (self.breadcrumbs_image.url))

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)  # Generate slug from title
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title

class TeamMembers(models.Model):
    team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='team_members', null=True)
    image = models.ImageField(upload_to=OUR_TEAM)
    name = models.CharField(unique=True, max_length=50)
    position = models.CharField(max_length=250)
    linkedin = models.URLField(max_length=200, blank=True, null=True)
    x = models.URLField(max_length=200, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Team Members"

    def team_image(self):
        return mark_safe(IMG_TAG_FORMAT % (self.image.url))

    def __str__(self):
        return self.name

#--- Team Model End -----




# --- Careers start --------
class Careers(models.Model):
    breadcrumbs_image = models.ImageField(upload_to=OUR_TEAM, null=True, blank=True)
    title = models.CharField('Job',max_length=250)
    location = models.CharField('Location', max_length=50, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Careers"

    def __str__(self):
        return self.title
    def breadcrumbs(self):
        return mark_safe(IMG_TAG_FORMAT % (self.breadcrumbs_image.url))



class JobApplication(models.Model):
    gender_choices = [
        ('Male', 'Male'),
        ('Female', 'Female'),
        ('Others', 'Others'),
    ]

    work_choice = [
        ('Yes', 'Yes'),
        ('No', 'No'),
    ]


    careers = models.ForeignKey(Careers, on_delete=models.CASCADE, related_name="job_application")
    job_summary =CKEditor5Field('Job Summary', config_name='extends', blank=True, null=True)
    gender = models.CharField(max_length=50, choices=gender_choices)
    first_name = models.CharField(max_length=250)
    middle_name = models.CharField(max_length=250, blank=True, null=True)
    last_name = models.CharField(max_length=250)
    email = models.EmailField()
    phone = models.CharField('Phone Number', max_length=20)
    address = models.TextField()
    resume_cv = models.FileField('Resume / CV', upload_to="careers/") 
    cover_letter = models.TextField()
    job_type = models.CharField(max_length=50, choices=work_choice)
    linkedin = models.URLField(null=True, blank=True)
    github = models.URLField(null=True, blank=True)
    website = models.URLField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True) 

    class Meta:
        verbose_name_plural = "Job Applications"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} {self.first_name} {self.last_name} - {self.careers.title}"

