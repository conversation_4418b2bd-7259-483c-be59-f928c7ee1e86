from django.test import TestCase
from .models import Profile

class TestProfileModel(TestCase):

    def test_create_profile(self):
        profile = Profile.objects.create(
            title="Test Profile",
            banner_heading="High Tech",
            
        )
        
        # Test that the fields were saved correctly
        self.assertEqual(profile.title, "Test Profile")
        
        # Test that the string representation is correct (should be "Test Profile" as per model)
        self.assertEqual(str(profile), "Test Profile")
        
        # Test that the object is an instance of Profile
        self.assertTrue(isinstance(profile, Profile))

