from django.shortcuts import render, get_object_or_404
from . import models
from django.views.decorators.http import require_GET


#Contact view
@require_GET
def contact_view(request):
    contact = models.Contact.objects.all()
    contact_address = models.ContactAddress.objects.all()

    contact_plural_name = models.Contact._meta.verbose_name_plural

    context = {
        'contact': contact,
        'contact_address': contact_address,
        'contact_plural_name': contact_plural_name,
    }

    return render(request, 'core/contact.html', context)


#Careers View
@require_GET
def careers_view(request):
    careers = models.Careers.objects.all()
    job_application = models.JobApplication.objects.all()

    #Plural names
    careers_plural_name = models.Careers._meta.verbose_name_plural

    context = {
        'careers': careers,
        'job_application': job_application,
        'careers_plural_name': careers_plural_name,
    }

    return render(request, 'core/careers.html', context)

#About Us view
@require_GET
def about_us_view(request, about_page_slug):

    about_page = get_object_or_404(models.AboutUs, slug=about_page_slug)
    our_values = about_page.our_values.all()
    
    context = {
        'about_page': about_page,
        'our_values': our_values,
        'about_page_slug': about_page_slug,
    }
    return render(request, 'core/about-us.html', context)


#Team View
@require_GET
def team_view(request, team_slug):

    team = get_object_or_404(models.Team, slug=team_slug)
    team_members = team.team_members.all()

    #Plural names
    team_plural_name = models.Team._meta.verbose_name_plural

    context = {
        'team': team,
        'team_members': team_members,
        'team_plural_name': team_plural_name,
        'team_slug': team_slug,
    }

    return render(request, 'core/our-team.html', context)





