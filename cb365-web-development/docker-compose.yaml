version: "3.9"

services:
  db:
    container_name: "cb365_db"
    image: mysql:8.0
    env_file:
      - .env
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}

    networks:
      - app_network
    volumes:
      - /mnt/mysql:/var/lib/mysql
    restart: always

  web:
    container_name: "cb365_app"
    image: cobber365/cobber365:latest
    depends_on:
      - db
    env_file:
      - .env
    environment:
      - HOST=${MYSQL_HOST}
      - NAME=${MYSQL_DATABASE}
      - USER=${MYSQL_USER}
      - PASSWORD=${MYSQL_PASSWORD}
      - PORT=3306
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - DEBUG=${DEBUG}
    ports:
      - "8000:8000"
    networks:
      - app_network
    volumes:
      - /mnt/app:/app
      - ./entrypoint.sh:/entrypoint.sh
    entrypoint: ["/bin/bash", "/entrypoint.sh"]
    restart: always

networks:
  app_network:
    name: app_network
    driver: bridge
