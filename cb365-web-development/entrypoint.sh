#!/bin/bash

#Wait for 30 seconds before attempting to connect to MySQL (in case MySQL takes time to initialize)
echo "Waiting for MySQL to be fully initialized (30 seconds)..."
sleep 30

# Apply database migrations
echo "Running migrations..."
python manage.py migrate --noinput

# Creating superuser
echo "Creating superuser for admin"
python superuser.py --noinput

# Start Gunicorn server
echo "Starting Gunicorn server..."
exec gunicorn cobber.wsgi:application --bind 0.0.0.0:8000
