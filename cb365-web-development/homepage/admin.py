from django.contrib import admin
from . import models
from django.utils.translation import gettext_lazy as _
from django.utils.safestring import mark_safe



# Register your models here.
#--Animated Message ---
class AnimatedTitleInline(admin.TabularInline):
    model = models.AnimatedTitle
    extra = 1
    max_num = 2

class CarouselImageInline(admin.TabularInline):
    model = models.CarouselImage
    extra = 1
    max_num = 10


#---- Hero-----
class HeroSectionAdmin(admin.ModelAdmin):
    list_display = ('title',) 
    inlines = [AnimatedTitleInline, CarouselImageInline ]

    def image_preview(self, obj):
        if obj.image:
            return mark_safe(f'<img src="{obj.image.url}" width="50px" />')
        return "No Image"

    image_preview.short_description = 'Image Preview'

    #Prevent adding new Hero Section
    def has_add_permission(self, request):
        return models.HeroSection.objects.count() == 0
    
#---Client-----
class SectorTypesInline(admin.StackedInline):
    model = models.SectorTypes
    extra = 0
    max_num = 5

class SectorsAdmin(admin.ModelAdmin):
    list_display =('title',)
    inlines = [SectorTypesInline]

    def has_add_permission(self, request):
        return models.Sectors.objects.count() == 0
    

#----Counter------
class CounterFeedInline(admin.TabularInline):
    model = models.CounterFeed
    extra = 0
    max_num = 4

class CounterAdmin(admin.ModelAdmin):
    list_display = ('title',)
    inlines = [CounterFeedInline]
    
    def has_add_permission(self, request):
        return models.Counter.objects.count() == 0


# -----Testimonial------
class TestimonialsInline(admin.StackedInline):
    model = models.TestimonialList
    extra = 0

class TestimonialsAdmin(admin.ModelAdmin):
    list_display = ('title', 'description')
    inlines = [TestimonialsInline]

    def has_add_permission(self, request):
        return models.Testimonials.objects.count() == 0


#----StackHomepage
class StackHomepageImagesInline(admin.TabularInline):
    model = models.StackHomepageImages
    extra = 0
    max_num = 15

class StackHomepageAdmin(admin.ModelAdmin):
    list_display = ('title',)
    inlines = [StackHomepageImagesInline]

    def has_add_permission(self, request):
        return models.StackHomepage.objects.count() == 0

#----About Us Homepage-----
class AboutUsHomepageAdmin(admin.ModelAdmin):
    list_display = ('about_us',)

# ----- Partners ---------
class PartnersAdmin(admin.ModelAdmin):
    list_display = ('title', 'image')
    search_fields = ('title',)



#Register model to admin view
admin.site.register(models.HeroSection, HeroSectionAdmin)
admin.site.register(models.Sectors, SectorsAdmin)
admin.site.register(models.Counter, CounterAdmin)
admin.site.register(models.Testimonials, TestimonialsAdmin)
admin.site.register(models.StackHomepage, StackHomepageAdmin)
admin.site.register(models.Partners, PartnersAdmin)

