from . import models

def global_view(request):
    counter = models.Counter.objects.all()
    counter_feed = models.CounterFeed.objects.all()
    testimonials = models.Testimonials.objects.all()
    testimonial_list = models.TestimonialList.objects.all()
    

    # Fetch all partners
    partners = models.Partners.objects.all()

    return {
        'counter': counter,
        'counter_feed': counter_feed,
        'testimonials': testimonials,
        'testimonial_list': testimonial_list,
        'partners': partners,
    }

def stack_homepage(request):  
    return {'media_files': models.StackHomepage.objects.all()}