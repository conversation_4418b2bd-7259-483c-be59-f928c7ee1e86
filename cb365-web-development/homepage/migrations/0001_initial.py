# Generated by Django 5.1 on 2025-01-04 10:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cobber365', '0004_remove_profile_banner_remove_profile_banner_body_and_more'),
        ('services_and_more', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Counter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField()),
            ],
            options={
                'verbose_name_plural': 'Counter Section',
            },
        ),
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('description', models.TextField(blank=True, max_length=500)),
                ('hero_video', models.FileField(blank=True, null=True, upload_to='hero/')),
                ('button', models.CharField(blank=True, max_length=250)),
            ],
            options={
                'verbose_name_plural': 'Hero Section',
            },
        ),
        migrations.CreateModel(
            name='Partners',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50)),
                ('image', models.ImageField(upload_to='partners')),
            ],
            options={
                'verbose_name_plural': ' Our Partners',
            },
        ),
        migrations.CreateModel(
            name='StackHomepage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('description', models.TextField(blank=True)),
                ('button', models.CharField(blank=True, max_length=250)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='stackfiles/')),
            ],
            options={
                'verbose_name_plural': 'Stack Homepage',
            },
        ),
        migrations.CreateModel(
            name='Testimonials',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'verbose_name_plural': 'Testimonial Section',
            },
        ),
        migrations.CreateModel(
            name='AboutUsHomepage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('about_us', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='about_us', to='cobber365.aboutus')),
            ],
            options={
                'verbose_name_plural': 'About Us Homepage',
            },
        ),
        migrations.CreateModel(
            name='CounterFeed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.IntegerField(blank=True, null=True)),
                ('feed', models.CharField(blank=True, max_length=50)),
                ('counter', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='counter_feed', to='homepage.counter')),
            ],
            options={
                'verbose_name_plural': 'Counter Feed',
            },
        ),
        migrations.CreateModel(
            name='ServicesHomepage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('services', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='services_homepage', to='services_and_more.services')),
            ],
            options={
                'verbose_name_plural': 'Services Homepage',
            },
        ),
        migrations.CreateModel(
            name='StackHomepageImages',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('image', models.ImageField(upload_to='stackfiles')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('stack_images', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='stack_images', to='homepage.stackhomepage')),
            ],
            options={
                'verbose_name_plural': 'StackHomepage Images',
            },
        ),
        migrations.CreateModel(
            name='TestimonialList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, upload_to='testimonial')),
                ('name', models.CharField(max_length=50)),
                ('title', models.CharField(blank=True, max_length=50)),
                ('body', models.TextField(blank=True, max_length=250)),
                ('rating', models.PositiveIntegerField(choices=[(1, '⭑'), (2, '⭑⭑'), (3, '⭑⭑⭑'), (4, '⭑⭑⭑⭑'), (5, '⭑⭑⭑⭑⭑')], default=None)),
                ('testimonials', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='testimonial_list', to='homepage.testimonials')),
            ],
            options={
                'verbose_name_plural': 'Testimonial List',
            },
        ),
    ]
