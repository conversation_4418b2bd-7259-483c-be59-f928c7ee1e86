# Generated by Django 5.1 on 2025-01-05 01:09

import django_ckeditor_5.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0002_remove_serviceshomepage_services_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Features',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('head', models.TextField(blank=True, max_length=250)),
                ('body', django_ckeditor_5.fields.CKEditor5Field(blank=True)),
                ('lottie_template', django_ckeditor_5.fields.CKEditor5Field(blank=True)),
            ],
            options={
                'verbose_name_plural': 'Features',
            },
        ),
    ]
