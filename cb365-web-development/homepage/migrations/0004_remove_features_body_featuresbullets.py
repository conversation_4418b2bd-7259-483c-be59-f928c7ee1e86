# Generated by Django 5.1 on 2025-01-05 09:27

import django.db.models.deletion
import django_ckeditor_5.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0003_features'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='features',
            name='body',
        ),
        migrations.CreateModel(
            name='FeaturesBullets',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bullets', django_ckeditor_5.fields.CKEditor5Field(blank=True)),
                ('features', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='Features_info', to='homepage.features')),
            ],
            options={
                'verbose_name_plural': 'Bullets',
            },
        ),
    ]
