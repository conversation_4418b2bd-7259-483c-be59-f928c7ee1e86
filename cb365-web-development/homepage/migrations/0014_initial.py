# Generated by Django 5.1 on 2025-01-20 18:37

import django.db.models.deletion
import django_ckeditor_5.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('homepage', '0013_remove_counterfeed_counter_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Counter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField()),
            ],
            options={
                'verbose_name_plural': 'Counter',
            },
        ),
        migrations.CreateModel(
            name='Features',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('head', models.CharField(blank=True, max_length=250)),
                ('svg_animation', models.Char<PERSON>ield(blank=True, max_length=500, null=True)),
            ],
            options={
                'verbose_name_plural': 'Features',
            },
        ),
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='hero/')),
                ('show_background_image', models.BooleanField(default=False)),
                ('title', models.CharField(max_length=250)),
                ('description', models.TextField(blank=True, max_length=500)),
                ('image', models.ImageField(blank=True, null=True, upload_to='hero/')),
                ('show_image', models.BooleanField(default=True)),
                ('svg_animation', models.CharField(blank=True, max_length=500, null=True)),
                ('show_svg_animation', models.BooleanField(default=False)),
                ('button', models.CharField(blank=True, max_length=250)),
            ],
            options={
                'verbose_name_plural': 'Hero Section',
            },
        ),
        migrations.CreateModel(
            name='Partners',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50)),
                ('image', models.ImageField(upload_to='partners')),
            ],
            options={
                'verbose_name_plural': 'Our Partners',
            },
        ),
        migrations.CreateModel(
            name='StackHomepage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('description', models.TextField(blank=True)),
                ('button', models.CharField(blank=True, max_length=250)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='stackfiles/')),
            ],
            options={
                'verbose_name_plural': 'Our Stack',
            },
        ),
        migrations.CreateModel(
            name='Testimonials',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'verbose_name_plural': 'Testimonials',
            },
        ),
        migrations.CreateModel(
            name='CounterFeed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.IntegerField(blank=True, null=True)),
                ('feed', models.CharField(blank=True, max_length=50)),
                ('counter', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='counter_feed', to='homepage.counter')),
            ],
            options={
                'verbose_name_plural': 'Counter Feed',
            },
        ),
        migrations.CreateModel(
            name='FeaturesBullets',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bullets', django_ckeditor_5.fields.CKEditor5Field(blank=True)),
                ('features', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='features_info', to='homepage.features')),
            ],
            options={
                'verbose_name_plural': 'Bullets',
            },
        ),
        migrations.CreateModel(
            name='StackHomepageImages',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('image', models.ImageField(upload_to='stackfiles')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('stack_images', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='stack_images', to='homepage.stackhomepage')),
            ],
            options={
                'verbose_name_plural': 'StackHomepage Images',
            },
        ),
        migrations.CreateModel(
            name='TestimonialList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, upload_to='testimonial')),
                ('name', models.CharField(max_length=50)),
                ('title', models.CharField(blank=True, max_length=50)),
                ('body', models.TextField(blank=True, max_length=250)),
                ('rating', models.PositiveIntegerField(choices=[(1, '⭑'), (2, '⭑⭑'), (3, '⭑⭑⭑'), (4, '⭑⭑⭑⭑'), (5, '⭑⭑⭑⭑⭑')], default=None)),
                ('testimonials', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='testimonial_list', to='homepage.testimonials')),
            ],
            options={
                'verbose_name_plural': 'Testimonial List',
            },
        ),
    ]
