from django.db import migrations, models

def create_fixed_features(apps, schema_editor):
    Features = apps.get_model('homepage', 'Features')
    Features.objects.get_or_create(id=1, defaults={'title': 'Development'})
    Features.objects.get_or_create(id=2, defaults={'title': 'DevOps & Cloud'})
    Features.objects.get_or_create(id=3, defaults={'title': 'Security'})

class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0017_animatedmessage'), 
    ]

    operations = [
        migrations.RunPython(create_fixed_features),
    ]
