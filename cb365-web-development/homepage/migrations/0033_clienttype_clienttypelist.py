# Generated by Django 5.1 on 2025-02-05 10:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0032_alter_testimoniallist_message'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClientType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField(max_length=100)),
            ],
            options={
                'verbose_name_plural': 'Who we serve',
            },
        ),
        migrations.CreateModel(
            name='ClientTypeList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('image', models.ImageField(upload_to='client/')),
                ('client_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='client_type', to='homepage.clienttype')),
            ],
            options={
                'verbose_name_plural': 'Client Type List',
            },
        ),
    ]
