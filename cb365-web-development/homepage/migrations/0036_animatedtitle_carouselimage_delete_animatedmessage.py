# Generated by Django 5.1 on 2025-02-24 14:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "homepage",
            "0035_remove_clienttypelist_client_type_delete_clienttype_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="AnimatedTitle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=250)),
                (
                    "animated_title",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="animated_title",
                        to="homepage.herosection",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Animated Title",
            },
        ),
        migrations.CreateModel(
            name="CarouselImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("images", models.ImageField(blank=True, null=True, upload_to="hero/")),
                (
                    "carousel_image",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="carousel_image",
                        to="homepage.herosection",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Add Carousel Image",
            },
        ),
        migrations.DeleteModel(
            name="AnimatedMessage",
        ),
    ]
