# Generated by Django 5.1 on 2025-02-24 16:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("homepage", "0037_remove_herosection_image_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Client",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.TextField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="ClientTypes",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.TextField(max_length=100)),
                ("image", models.ImageField(upload_to="hero/")),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="Client_type",
                        to="homepage.client",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Type of Clients",
            },
        ),
    ]
