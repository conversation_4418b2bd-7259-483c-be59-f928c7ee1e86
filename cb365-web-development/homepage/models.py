from django.db import models
from django.utils.html import mark_safe
from django.core.exceptions import ValidationError
from PIL import Image
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import PermissionDenied



IMG_TAG_FORMAT = '<img src="%s" />'

# Create your models here.

RATING = (
    (1, "⭑"),
    (2, "⭑⭑"),
    (3, "⭑⭑⭑"),
    (4, "⭑⭑⭑⭑"),
    (5, "⭑⭑⭑⭑⭑"),
)


# ----Hero Section -----
class HeroSection(models.Model):
    title = models.CharField(max_length=250)
    description = models.TextField(max_length=500, blank=True)
    button = models.CharField(max_length=250, blank=True)

    class Meta:
        verbose_name_plural = "Hero Section"

    def __str__(self):
        return self.title
    
    

#----- Banner carousel -----
class CarouselImage(models.Model):
    carousel_image = models.ForeignKey(HeroSection, on_delete=models.CASCADE, related_name="carousel_image", null=True, blank=True)
    images = models.ImageField(upload_to="hero/", null=True, blank=True)

    class Meta:
        verbose_name_plural = "Add Carousel Image"
    
    def carousel_images(self):
        return mark_safe(f'<img src="{self.images.url}" alt="" class="carousel-image">')
    
    

#--- Animated Message -----
class AnimatedTitle(models.Model):
    animated_title = models.ForeignKey(HeroSection, on_delete=models.CASCADE, related_name="animated_title")
    title = models.CharField(max_length=250)

    class Meta:
        verbose_name_plural = "Animated Title"

    def __str__(self):
        return self.title


#---Sectors------
class Sectors(models.Model):
    title = models.TextField(max_length=100)

    class Meta:
        verbose_name_plural = "Sectors"

    def __str__(self):
        return self.title
    
class SectorTypes(models.Model):
    client = models.ForeignKey(Sectors, on_delete=models.CASCADE, related_name="Client_type")
    name = models.TextField(max_length=100)
    image = models.ImageField(upload_to="hero/")

    class Meta:
        verbose_name_plural = "Sector Type"

    def __str__(self):
        return self.name
    
    def client_image(self):
        return mark_safe(f'<img src="{self.image.url}" alt="" class="sector-image">')
      

#--- Counter Section ----
class Counter(models.Model):
    title = models.TextField()

    class Meta:
        verbose_name_plural = "Counter"

    def __str__(self):
        return self.title
    
class CounterFeed(models.Model):
    counter = models.ForeignKey(Counter, related_name="counter_feed", on_delete=models.CASCADE, null=True)
    number = models.IntegerField(null=True, blank=True)
    feed = models.TextField(max_length=50, blank=True)

    class Meta:
        verbose_name_plural = "Counter Feed"

    def __str__(self):
        return self.feed


# ---- Testimonial Section ------
class Testimonials(models.Model):
    title = models.CharField(max_length=250)
    description = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = "Testimonials"

    def __str__(self):
        return self.title

class TestimonialList(models.Model):
    testimonials = models.ForeignKey(Testimonials, on_delete=models.CASCADE, related_name="testimonial_list")
    image = models.ImageField(upload_to="testimonial", blank=True, null=True)
    name = models.CharField(max_length=50)
    profession = models.CharField(max_length=50, blank=True)
    message = models.TextField(max_length=200, blank=True)
    rating = models.PositiveIntegerField(choices=RATING, default=None)


    class Meta:
        verbose_name_plural = "Testimonial List"

    def __str__(self):
        return self.name
    
    def get_rating(self):
        return self.rating
    
    def clean(self):
        if self.rating not in dict(RATING).keys():
            raise ValidationError({'rating': 'Invalid rating value.'})
        
    

# --- Stack Homepage ----
class StackHomepage(models.Model):
    title = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    button = models.CharField(blank=True, max_length=250)
    
    class Meta:
        verbose_name_plural = "Our Stack"

    def image(self):
        return mark_safe(IMG_TAG_FORMAT % (self.background_image.url))

    def __str__(self):
        return self.title

class StackHomepageImages(models.Model):
    stack_images = models.ForeignKey(StackHomepage, on_delete=models.CASCADE, related_name='stack_images', blank=True )
    name = models.CharField(max_length=50)
    image = models.ImageField(upload_to='stackfiles')

    class Meta:
        verbose_name_plural = "Stack Homepage Images"

    def media_image(self):
        return mark_safe(IMG_TAG_FORMAT % (self.image.url))

    def __str__(self):
        return self.name
        



# -- Partners Section ----
class Partners(models.Model):
    title = models.CharField(max_length=50)
    image = models.ImageField(upload_to='partners')

    class Meta:
        verbose_name_plural = "Our Partners"

    def stack_image(self):
        return mark_safe(IMG_TAG_FORMAT % (self.image.url))

    def __str__(self):
        return self.title

    


    
