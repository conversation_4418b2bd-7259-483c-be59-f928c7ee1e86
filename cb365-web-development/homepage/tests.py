from django.test import TestCase
from .models import Testimonials, TestimonialList, Partners
from django.core.exceptions import ValidationError
from django.db import IntegrityError

# Create your tests here.
RATING = (
    (1, "⭑"),
    (2, "⭑⭑"),
    (3, "⭑⭑⭑"),
    (4, "⭑⭑⭑⭑"),
    (5, "⭑⭑⭑⭑⭑"),
)


class TestimonialsModelTests(TestCase):
    def test_create_testimonials(self):
        testimonial = Testimonials.objects.create(
            title = "Feedback",
            description = "Hear what our client are saying about us",
        )

        self.assertEqual(str(testimonial), "Feedback")
        self.assertTrue(isinstance(testimonial, Testimonials))


    def test_create_testimonial_list(self):
        testimonial = Testimonials.objects.create(
            title = "Feedback",
            description = "Hear what our client are saying about us",
        )

        testimonial_list = TestimonialList.objects.create(
            testimonials=testimonial,
            name = "<PERSON><PERSON>",
            title = "CEO Chopiss Group",
            body = "Amazing working with you guys",
            rating = 5,
        )
        
        self.assertEqual(str(testimonial_list), "Amaka <PERSON>biora")
        self.assertTrue(isinstance(testimonial_list, TestimonialList))
        self.assertEqual(testimonial_list.get_rating(), 5)


    def test_invalid_rating(self):
        # Test an invalid rating for TestimonialList
        testimonial = Testimonials.objects.create(
            title="Outstanding Service",
            description="Highly recommend this service!"
        )

        # Attempt to create a testimonial list with an invalid rating
        try:
            testimonial_list = TestimonialList.objects.create(
                testimonials=testimonial,
                name="Alice Green",
                title="Happy Customer",
                body="The service exceeded my expectations!",
                rating=6 
            )
            testimonial_list.full_clean() 
            self.fail("ValidationError not raised for invalid rating")
        except ValidationError:
            pass
        except IntegrityError:
            self.fail("Unexpected IntegrityError raised")