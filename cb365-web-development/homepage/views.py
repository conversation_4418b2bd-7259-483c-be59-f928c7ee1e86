from django.shortcuts import render, get_object_or_404
from django.views.decorators.http import require_GET
from . import models 



# Create your views here.
#-----Hero Section------
@require_GET
def index(request):
    hero_section = models.HeroSection.objects.all()
    animated_title = models.AnimatedTitle.objects.all()
    counter = models.Counter.objects.all()
    counter_feed = models.CounterFeed.objects.all()
    stack_homepage = models.StackHomepage.objects.all()
    stack_homepage_images = models.StackHomepageImages.objects.all()
    carousel_images = models.CarouselImage.objects.all()
    sectors = models.Sectors.objects.all()
    sector_types = models.SectorTypes.objects.all()
    

    context = {
        'hero_section': hero_section,
        'animated_title': animated_title,
        'counter': counter,
        'counter_feed': counter_feed,
        'stack_homepage': stack_homepage,
        'stack_homepage_images': stack_homepage_images,
        'carousel_images': carousel_images,
        'sectors': sectors,
        'sector_types': sector_types,
    
    }

    return render(request, 'core/index.html', context)

