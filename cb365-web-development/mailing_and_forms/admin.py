from django.utils.html import format_html
from django.contrib import admin
from . import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.mail import send_mail
from django_ckeditor_5.fields import CKEditor5Widget
from django import forms

# Register your models here.


#NewsLetter
class NewsletterAdmin(admin.ModelAdmin):
    list_display = ('subscriber', 'subscribed_at')


class EmailTemplateAdminForm(forms.ModelForm):
    class Meta:
        model = models.EmailTemplate
        fields= ('subject', 'message', 'recipients')
        widgets = {
            'message': CKEditor5Widget(),
        }

class EmailTemplateAdmin(admin.ModelAdmin):
    form = EmailTemplateAdminForm

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        subject = obj.subject
        html_message = obj.message

        recipients = [subscriber.email for subscriber in obj.recipient.all()]
        from_email = settings.EMAIL_HOST_USER
        send_mail(subject, "", from_email, recipients, fail_silently=False, html_message=html_message)



#--Contact Admin
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('name', 'subject', 'received_at')

    def truncated_message(self, obj):
        return format_html('<span>{}</span>', obj.message[:100] + '...' if len(obj.message) > 100 else obj.message)
    truncated_message.short_description = 'Message'



class PartnershipApplicationsAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'company', 'received_at')


#Register Mailing and froms admin view
admin.site.register(models.NewsletterSubscriber, NewsletterAdmin)
admin.site.register(models.EmailTemplate, EmailTemplateAdmin)
admin.site.register(models.Project, ProjectAdmin)
admin.site.register(models.PartnershipApplications, PartnershipApplicationsAdmin)