from django import forms
from . import models

class ProjectForm(forms.ModelForm):
    class Meta:
        model = models.Project
        fields = ['name', 'email', 'phone', 'subject', 'message']
 


#Newsletter form
class NewsletterForm(forms.ModelForm):
    class Meta:
        model = models.NewsletterSubscriber
        fields = ['subscriber']

    # Custom validation to check if the email is already subscribed
    def clean_subscriber(self):
        email = self.cleaned_data.get('subscriber')
        if models.NewsletterSubscriber.objects.filter(subscriber=email).exists():
            raise forms.ValidationError("This email has already subscribed.")
        return email



#Partnership
class PartnershipApplicationForm(forms.ModelForm):
    class Meta:
        model = models.PartnershipApplications
        fields = ['first_name', 'last_name', 'email', 'phone', 'company', 'message']

