# Generated by Django 5.1 on 2025-01-20 17:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mailing_and_forms', '0004_delete_contact'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.Char<PERSON>ield(max_length=15)),
                ('subject', models.Char<PERSON>ield(max_length=100)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Contact Us',
            },
        ),
    ]
