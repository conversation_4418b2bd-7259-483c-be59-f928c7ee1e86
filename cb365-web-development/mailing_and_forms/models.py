from django.db import models
from django.utils.html import mark_safe
from django.core.exceptions import ValidationError
from django.core.validators import EmailValidator
import re
from django.utils.html import format_html
from PIL import Image
import io
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import CKEditor5<PERSON>ield

# Create your models here.

# --- Porject Start ----
class Project(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=15)
    subject = models.CharField(max_length=100)
    message = models.TextField()
    received_at = models.DateTimeField(auto_now_add=True, null=True)

    # Optional: Custom phone number validation (e.g., for a simple phone format)
    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # A simple regex for phone validation (adjust it as needed)
            phone_regex = re.compile(r'^\+?1?\d{9,15}$')
            if not phone_regex.match(phone):
                raise ValidationError('Invalid phone number format.')
        return phone

    class Meta:
        verbose_name_plural = "Discuss a Project"



# --- Newsletter Start ----
class NewsletterSubscriber(models.Model):
    subscriber = models.EmailField(unique=True)
    subscribed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Newsletter Subscribers"

    def __str__(self):
        return self.subscriber



class EmailTemplate(models.Model):
    subject = models.CharField(max_length=250)
    message = CKEditor5Field('Message', config_name='extends', blank=True, null=True)
    recipients = models.ManyToManyField(NewsletterSubscriber)



class PartnershipApplications(models.Model):
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=100)
    company = models.CharField(max_length=250)
    message = models.TextField()
    received_at = models.DateTimeField(auto_now_add=True, null=True)

    class Meta:
        verbose_name_plural = "Partnership Applications"

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # A simple regex for phone validation (adjust it as needed)
            phone_regex = re.compile(r'^\+?1?\d{9,15}$')
            if not phone_regex.match(phone):
                raise ValidationError('Invalid phone number format.')
        return phone
    
    def __str__(self):
        return self.first_name
