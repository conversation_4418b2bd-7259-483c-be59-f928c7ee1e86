from django.shortcuts import render, render
from django.core.mail import send_mail
from django.conf import settings
from . import forms
from django.core.mail import EmailMessage
from django.contrib import messages
from django.template.loader import render_to_string
from django.http import JsonResponse
from django_ratelimit.decorators import ratelimit
from django_ratelimit.exceptions import Ratelimited


CONTACT_EMAIL = '<EMAIL>'
PROJECT_HTML = 'core/project.html'

#Project view View
@ratelimit(key='ip', rate='2/m', block=True)
def project_view(request):
    if getattr(request, 'limited', False):
        # If the user is rate-limited
        messages.error(request, "You are submitting too fast. Please try again later.")
        return render(request, PROJECT_HTML, {'form': forms.ProjectForm()})
    
    if request.method == 'POST':
        form = forms.ProjectForm(request.POST)
        if form.is_valid():
            name = form.cleaned_data['name']
            email = form.cleaned_data['email']
            phone = form.cleaned_data['phone']
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            email_body = f"""
            Name: {name}
            Email: {email}
            Phone: {phone}
            Subject: {subject}
            Message: {message}
            """

            EmailMessage(
                subject= 'Project Form Submission',
                body=email_body,
                from_email=email, 
                to=[CONTACT_EMAIL], 
                bcc=[],
                reply_to=[email]
            ).send(fail_silently=True)
            
            messages.success(request, "Message sent!")
            form.save()

            form = forms.ProjectForm()

            context = {
                'form': form,
            }
            return render(request, PROJECT_HTML, context)
    else:
        form = forms.ProjectForm()   
    return render(request, PROJECT_HTML, {'form': form})


PARTNERSHIP_HTML = 'core/partnership.html'
#Partnership view
@ratelimit(key='ip', rate='2/m', block=True)
def partnership_view(request):
    if getattr(request, 'limited', False):
        messages.error(request, "You are submitting too fast. Please try again later.")
        return render(request, PARTNERSHIP_HTML, {'form': forms.PartnershipApplicationForm()})
    
    if request.method == "POST":
        form = forms.PartnershipApplicationForm(request.POST)

        if form.is_valid():
            first_name = form.cleaned_data['first_name']
            last_name = form.cleaned_data['last_name']
            email = form.cleaned_data['email']
            phone = form.cleaned_data['phone']
            company = form.cleaned_data['company']
            message = form.cleaned_data['message']

            email_body = f"""
            Name: {first_name} {last_name}
            Email: {email}
            Phone: {phone}
            Company: {company}
            Message: {message}
            """

            EmailMessage(
                subject= "Partnership Application",
                body=email_body,
                from_email=email, 
                to=[CONTACT_EMAIL], 
                bcc=[],
                reply_to=[email]
            ).send(fail_silently=True)

            messages.success(request, "Thanks for applying. You will get a response from us soon.")
            form.save()

            context = {
                'form': form,
            }
            return render(request, PARTNERSHIP_HTML, context)
        
    else:
        form = forms.PartnershipApplicationForm()
    return render(request, PARTNERSHIP_HTML, {'form':form})


#---Newsletter------
@ratelimit(key='ip', rate='2/m', block=True)
def newsletter_view(request):
    if request.method == 'POST':
        newsletter_subscriber = forms.NewsletterForm(request.POST)
        
        if newsletter_subscriber.is_valid():
            email = newsletter_subscriber.cleaned_data['subscriber']

            EmailMessage (
                '1 new subscriber',
                email + ' Subscribed to our newsletter',
                to= [CONTACT_EMAIL],
            ).send(fail_silently=True)
            
            #Send an email alert about new subscriber
            email_content = render_to_string('core/newsletter-signup.html', {'email': email})
            email_subject = "Thank you for subscribing!"
            recipient_list = [email]
            from_email = settings.EMAIL_HOST_USER

            try:
                # Send the email (HTML message)
                send_mail(
                    email_subject,
                    '',
                    from_email,
                    recipient_list,
                    html_message=email_content,
                    fail_silently=False,
                )
                newsletter_subscriber.save()

                # Return success message as JSON response
                return JsonResponse({'message': 'Thanks for subscribing!'}, status=200)

            except Exception:
                # Handle failure to send email
                return JsonResponse({'error': 'There was an error sending the email. Please try again later.'}, status=500)
        
        else:
            # If the form is invalid, return form errors
            return JsonResponse({'errors': newsletter_subscriber.errors}, status=400)

    else:
        # For GET requests, render the form as usual
        context = {'newsletter_subscriber': forms.NewsletterForm()}
        return render(request, 'core/newsletter.html', context)