events {
    worker_connections 1024;
}
# Global configuration for HTTP to HTTPS redirection
http {
    include /etc/nginx/mime.types;
    server {
        listen 80;
        server_name cobber365.com www.cobber365.com;

        # HTTP to HTTPS redirect
        return 301 https://$host$request_uri;
    }

    # SSL-enabled server for handling secure traffic (HTTPS)
    server {
    listen 443 ssl; # managed by Certbot
    server_name cobber365.com www.cobber365.com;
    
    ssl_certificate /etc/letsencrypt/live/cobber365.com-0001/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/cobber365.com-0001/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot


        # Static files location
        location /static/ {
            alias /var/www/html/nginx/staticfiles/;
            expires off;
            access_log off;
        }

        # Media files location
        location /media/ {
            alias /var/www/html/nginx/media/;
            expires off;
            access_log off;
        }

        # Main proxy to Django application
        location / {
            proxy_pass http://**********:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port 443;
        }
    }
}