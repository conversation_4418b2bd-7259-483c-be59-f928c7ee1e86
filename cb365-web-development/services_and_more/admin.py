from django.contrib import admin
from .models import Services, ServiceContent, ServiceImages, ServiceExtras, Stack, StackList
from django.utils.safestring import mark_safe

# Register your models here.

# --- Services Admin Start ----
class ServiceExtrasInline(admin.TabularInline):
    model = ServiceExtras
    extra = 0  
    max_num = 4

class ServiceImagesInline(admin.TabularInline):
    model = ServiceImages
    extra = 0
    fields = ('image_preview','image')
    readonly_fields = ('image_preview',)
    max_num = 10 
    
    def image_preview(self, obj):
        if obj.image:
            return mark_safe(f'<img src="{obj.image.url}" width="50px" />')
        return "No Image"

    image_preview.short_description = 'Image Preview'

class ServiceContentInline(admin.StackedInline):
    model = ServiceContent
    extra = 0  

class ServicesAdmin(admin.ModelAdmin):
    list_display = ('title', 'description')
    inlines = [ServiceContentInline] 

class ServiceContentAdmin(admin.ModelAdmin):
    list_display = ('title',)
    inlines = [ServiceImagesInline, ServiceExtrasInline]

# --- Services End ------



# ----- Stack Admin Start ------
class StackInline(admin.StackedInline):
    model = StackList
    extra = 0

class StackAdmin(admin.ModelAdmin):
    list_display = ('title',)
    inlines = [StackInline]



admin.site.register(Services, ServicesAdmin)
admin.site.register(Stack, StackAdmin)
admin.site.register(ServiceContent, ServiceContentAdmin)