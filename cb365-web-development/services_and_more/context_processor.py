from . import models
from django.http import HttpResponseNotAllowed

def global_view(request):
    
    services = models.Services.objects.all()
    stack = models.Stack.objects.all()

    #Plural names
    services_plural_name = models.Services._meta.verbose_name_plural
    stack_plural_name = models.Stack._meta.verbose_name_plural


    return {
        'services': services,
        'services_plural_name': services_plural_name,
        'stack': stack,
        'stack_plural_name': stack_plural_name,
    }
