# Generated by Django 5.1 on 2024-12-09 17:46

import django.db.models.deletion
import django_ckeditor_5.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Industries',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('banner', models.ImageField(blank=True, null=True, upload_to='industry/')),
                ('title', models.CharField(max_length=50, unique=True)),
                ('body', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body')),
                ('slug', models.SlugField(blank=True, null=True, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Industries',
            },
        ),
        migrations.CreateModel(
            name='ServiceContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=200, null=True)),
                ('body', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body')),
                ('image', models.ImageField(blank=True, null=True, upload_to='service_content/')),
                ('order', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Services',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to='services/')),
                ('banner', models.ImageField(blank=True, null=True, upload_to='services/')),
                ('title', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, max_length=250, null=True)),
                ('slug', models.SlugField(blank=True, null=True, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Our Services',
            },
        ),
        migrations.CreateModel(
            name='Stack',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50)),
                ('image', models.ImageField(blank=True, null=True, upload_to='stack')),
            ],
            options={
                'verbose_name_plural': 'Technology Stack',
            },
        ),
        migrations.CreateModel(
            name='AppList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='industries/')),
                ('title', models.CharField(max_length=50, unique=True)),
                ('body', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body')),
                ('website', models.URLField(blank=True, default='https://', null=True)),
                ('industries', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='app_list', to='services_and_more.industries')),
            ],
            options={
                'verbose_name_plural': 'App List',
            },
        ),
        migrations.CreateModel(
            name='ServiceExtras',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=100, null=True)),
                ('body', models.TextField(blank=True, max_length=150, null=True)),
                ('service_content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='extras', to='services_and_more.servicecontent')),
            ],
            options={
                'verbose_name_plural': 'Service Content Extras',
            },
        ),
        migrations.AddField(
            model_name='servicecontent',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content', to='services_and_more.services'),
        ),
        migrations.CreateModel(
            name='StackList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('body', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body')),
                ('stack', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stack_list', to='services_and_more.stack')),
            ],
            options={
                'verbose_name_plural': ' Stack List',
            },
        ),
    ]
