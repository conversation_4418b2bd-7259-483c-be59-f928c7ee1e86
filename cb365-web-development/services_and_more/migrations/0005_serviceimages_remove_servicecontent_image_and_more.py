# Generated by Django 5.1 on 2025-01-29 15:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services_and_more', '0004_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceImages',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, upload_to='services/')),
            ],
        ),
        migrations.RemoveField(
            model_name='servicecontent',
            name='image',
        ),
        migrations.AddField(
            model_name='servicecontent',
            name='images',
            field=models.ManyToManyField(blank=True, to='services_and_more.serviceimages'),
        ),
    ]
