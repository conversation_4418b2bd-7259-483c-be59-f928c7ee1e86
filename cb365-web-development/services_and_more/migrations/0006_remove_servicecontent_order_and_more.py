# Generated by Django 5.1 on 2025-01-29 15:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services_and_more', '0005_serviceimages_remove_servicecontent_image_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='servicecontent',
            name='order',
        ),
        migrations.RemoveField(
            model_name='servicecontent',
            name='images',
        ),
        migrations.AddField(
            model_name='servicecontent',
            name='images',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='services_and_more.serviceimages'),
        ),
    ]
