# Generated by Django 5.1 on 2025-01-29 16:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services_and_more', '0006_remove_servicecontent_order_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='serviceimages',
            options={'verbose_name_plural': 'Add Image(s)'},
        ),
        migrations.RemoveField(
            model_name='servicecontent',
            name='images',
        ),
        migrations.AddField(
            model_name='serviceimages',
            name='service_content',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='service_images', to='services_and_more.servicecontent'),
        ),
    ]
