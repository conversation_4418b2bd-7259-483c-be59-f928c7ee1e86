from django.db import models
from django.utils.html import mark_safe
import re
from django.utils.html import format_html
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import CKE<PERSON><PERSON>5<PERSON>ield
from PIL import Image

# Create your models here.
IMG_TAG_FORMAT = '<img src="%s" />'

SERVICES = "services/"

# --- Service Model Start ----
class Services(models.Model):
    title = models.CharField(unique=True, max_length=50)
    description = models.TextField(max_length=250, blank=True, null=True)
    slug = models.SlugField(unique=True, blank=True, null=True)
    breadcrumbs_image = models.ImageField(upload_to=SERVICES, null=True, blank=True)
    
    class Meta:
        verbose_name_plural = "Services"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    def breadcrumbs(self):
        return mark_safe(IMG_TAG_FORMAT % (self.breadcrumbs_image.url))
    

class ServiceContent(models.Model):
    service = models.ForeignKey(Services, related_name='content', on_delete=models.CASCADE)
    title = models.CharField(max_length=200, blank=True, null=True)
    body = CKEditor5Field('Body', config_name='extends', blank=True, null=True)
    image = models.ImageField(upload_to=SERVICES, blank=True, null=True)

    def content_image(self):
        return mark_safe(IMG_TAG_FORMAT % (self.image.url))

    def __str__(self):
        return self.title
    
class ServiceImages(models.Model):
    service_content = models.ForeignKey(ServiceContent, on_delete=models.CASCADE, related_name="service_images", null=True)
    image = models.ImageField(upload_to=SERVICES, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Multi Images"

    def service_images(self):
        return mark_safe(IMG_TAG_FORMAT % (self.image.url))

    def __str__(self):
        return str(self.image)


class ServiceExtras(models.Model):
    service_content = models.ForeignKey(ServiceContent, related_name="extras", on_delete=models.CASCADE)
    title = models.CharField(max_length=100, blank=True, null=True)
    text = models.TextField(max_length=150, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Service Extras"

    def __str__(self):
        return self.title
# --- Service Model End ----



# -- Stack Start ------
class Stack(models.Model):
    title = models.CharField(max_length=50)

    class Meta:
        verbose_name_plural = "Technology Stack"

    def __str__(self):
        return self.title
    

class StackList(models.Model):
    stack = models.ForeignKey(Stack, on_delete=models.CASCADE, related_name="stack_list")
    title = models.CharField(max_length=250)
    body = CKEditor5Field('Body', config_name='extends', blank=True, null=True)
    
    class Meta:
        verbose_name_plural = " Stack List"

    def __str__(self):
        return self.title
