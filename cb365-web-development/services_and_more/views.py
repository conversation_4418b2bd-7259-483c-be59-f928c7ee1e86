from django.shortcuts import render, render, get_object_or_404
from .models import Services, Stack, StackList
from django.views.decorators.http import require_GET

# Create your views here.

#Services View
@require_GET
def services_view(request, service_slug):
    service = get_object_or_404(Services, slug=service_slug)
    service_content = service.content.all()

    service_images = []
    extras = []

    for content in service_content:
        service_images.extend(content.service_images.all())
        extras.extend(content.extras.all())

    context = {
        'service': service,
        'service_content': service_content,
        'service_images': service_images,
        'extras': extras,
        'service_slug': service_slug,
    }

    return render(request, 'core/service-detail.html', context)



# Stack view
@require_GET
def stack_view(request):
    
    stack = Stack.objects.all()
    stack_list = StackList.objects.all()

    #Plural names
    stack_plural_name = Stack._meta.verbose_name_plural
    

    context = {
        # 'stack': stack,
        'stack': stack,
        'stack_list': stack_list,
        'stack_plural_name': stack_plural_name,
    }

    return render(request, 'core/stack.html', context)