from django.utils.html import format_html
from django.contrib import admin
from .models import Pages, PageExtras
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import CKEditor5Widget



# Register your models here.


# -- Pages Admin ------
class PageExtrasInline(admin.TabularInline):
    model = PageExtras
    extra = 1  

class PagesAdmin(admin.ModelAdmin):
    list_display = ('title', 'updated_at')
    inlines = [PageExtrasInline]
    

#Register Src Admin view
admin.site.register(Pages, PagesAdmin)