# Generated by Django 5.1 on 2024-12-09 17:46

import django.db.models.deletion
import django_ckeditor_5.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Counter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField()),
            ],
            options={
                'verbose_name_plural': 'Counter',
            },
        ),
        migrations.CreateModel(
            name='MediaFiles',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='mediafiles/')),
                ('description', models.TextField(blank=True, null=True)),
                ('button', models.CharField(blank=True, max_length=250, null=True)),
            ],
            options={
                'verbose_name_plural': 'Media Files',
            },
        ),
        migrations.CreateModel(
            name='Pages',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('body1', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body 1')),
                ('body2', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body 2')),
                ('link', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('slug', models.SlugField(blank=True, null=True, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Pages',
            },
        ),
        migrations.CreateModel(
            name='CounterFeed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.IntegerField(blank=True, null=True)),
                ('feed', models.CharField(blank=True, max_length=50, null=True)),
                ('counter', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='counter_feed', to='src.counter')),
            ],
            options={
                'verbose_name_plural': 'Counter Feed',
            },
        ),
        migrations.CreateModel(
            name='MediaImages',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('image', models.ImageField(upload_to='mediafiles')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('media_images', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='media_images', to='src.mediafiles')),
            ],
            options={
                'verbose_name_plural': 'Media Images',
            },
        ),
        migrations.CreateModel(
            name='PageExtras',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('extras', models.CharField(max_length=500)),
                ('pages', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='extras', to='src.pages')),
            ],
        ),
    ]
