# Generated by Django 5.1 on 2025-01-20 18:31

import django.db.models.deletion
import django_ckeditor_5.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('src', '0003_delete_pageextras_delete_pages'),
    ]

    operations = [
        migrations.CreateModel(
            name='Pages',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250)),
                ('body1', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body 1')),
                ('body2', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Body 2')),
                ('link', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('slug', models.SlugField(blank=True, null=True, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Pages',
            },
        ),
        migrations.CreateModel(
            name='PageExtras',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('extras', models.CharField(max_length=500)),
                ('pages', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='extras', to='src.pages')),
            ],
        ),
    ]
