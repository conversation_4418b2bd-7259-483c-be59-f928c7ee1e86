from django.db import models
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import CKE<PERSON>or5<PERSON>ield
from django.core.exceptions import PermissionDenied

# Create your models here.
IMG_TAG_FORMAT = '<img src="%s" />'

# -- Pages Model Start ----
from django.db import models

class Pages(models.Model):
    title = models.CharField(max_length=250)
    body1 = CKEditor5Field('Body 1', config_name='extends', blank=True, null=True)
    body2 = CKEditor5Field('Body 2', config_name=['extends', 'default'], blank=True, null=True)
    link = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True, null=True)

    class Meta:
        verbose_name_plural = 'Pages'

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if self.id in [1]:
            raise PermissionDenied("You cannot delete this feature.")
        super().delete(*args, **kwargs)

    def __str__(self):
        return self.title

class PageExtras(models.Model):
    pages = models.ForeignKey(Pages, related_name="extras", on_delete=models.CASCADE)
    extras = models.CharField(max_length=500)

    def __str__(self):
        return self.extras





    






