from django.shortcuts import render, get_object_or_404
from src.models import Pages
from django.http import HttpResponseNotAllowed
from django.views.decorators.http import require_GET





# Pages views
@require_GET
def pages_view(request, pages_slug):
    
    pages = get_object_or_404(Pages, slug=pages_slug)
    pages_extras = pages.extras.all()

    context = {
        'pages': pages,
        'pages_extras': pages_extras,
    }

    return render(request, 'core/pages.html', context)