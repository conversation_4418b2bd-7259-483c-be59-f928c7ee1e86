/* About us */
.about-us {
  min-height: 100%;
  width: 100%;
  padding-bottom: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.about-us h1 {
  text-align: center;
  font-weight: 700;
  padding-top: 20px;
  padding-bottom: 20px;
}

.about-us-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
  padding-bottom: 5%;
  width: 100%;
}

.about-us-items > div {
  flex: 1;
  min-width: 300px;
}

.about-us .about-us-items img {
  height: 500px;
  max-width: 100%;
  border-radius: 10px;
  object-fit: cover;
}

@media (max-width: 1024px) {
  .about-us .about-us-items img {
    max-width: 100%;
    height: 350px;
    object-fit: cover;
  }
}

@media (max-width: 820px) {
  .about-us-items {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .about-us-items > div {
    min-width: unset;
    max-width: 100%;
  }

  .about-us .about-us-items img {
    max-width: 100%;
    height: 500px;
  }

  .about-us h1 {
    font-size: 24px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

@media (max-width: 512px) {
  .about-us-items {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .about-us-items > div {
    min-width: unset;
    max-width: 100%;
  }

  .about-us .about-us-items img {
    max-width: 100%;
    height: 300px;
  }

  .about-us h1 {
    font-size: 24px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

.values {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgb(242, 242, 251);
  padding-bottom: 50px;
  justify-content: center;
  align-items: center;
}

.values .values-items h1 {
  text-align: center;
  font-weight: 700;
  padding-top: 50px;
  padding-bottom: 20px;
}

.values-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  list-style: none;
  justify-content: center;
  padding: 0;
  margin: 0;
  width: 100%;
}

.values-list-items {
  display: flex;
  gap: 10px;
  list-style: none;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
}

.values-list-items i {
  font-size: 25px;
  color: #d7343a;
}

.values-list-items h5 {
  font-size: 20px;
  font-weight: 500;
}

.values-list li {
  border-radius: 10px;
  padding: 10px;
  background-color: white;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .values-list {
    grid-template-columns: 1fr;
  }

  .values-list li {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .values-list-items {
    width: 100%;
  }
}

/* Partnership*/
.partnership {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.partnership-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px 50px;
}

.partnership-items h1 {
  font-weight: 700;
}

.partnership-items ul {
  list-style: square;
}
