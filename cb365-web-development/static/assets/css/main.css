/* Fonts */
:root {
  /*Smooth scroll*/
  scroll-behavior: smooth;

  --default-font: "Lato", "Roboto Condensed", "Open Sans", system-ui,
    -apple-system, "Roboto", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Exo 2", "Orbitron", "Roboto", "Montserrat", "Raleway",
    "Poppins", sans-serif;
  --nav-font: "Lato", "Open Sans", sans-serif;

  /* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
  --background-color: #ffffff; /* Background color for the entire website, including individual sections */
  --default-color: #444444; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #2a2c39; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #3590f4;
  --button-color: linear-gradient(
    to right,
    #38b6ff,
    #326aea
  ); /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #ffffff; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
  --link-color: green;
  /* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
  --nav-color: #474747; /* The default color of the main navmenu links */
  --nav-hover-color: #3590f4;
  --nav-mobile-background-color: #ffffff; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #ffffff; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #060606; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #3590f4; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
  --background2-color: rgb(249, 249, 255); /* Light-blue backgound*/
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */
.light-background {
  --background-color: #f9f9f9;
  --surface-color: #ffffff;
}

.dark-background {
  --background-color: #3590f4;
  --default-color: white;
  --heading-color: black;
  --surface-color: #404356;
  --contrast-color: black;
  --link-color: green;
}

/* Smooth scroll */

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
#header {
  transition: top 0.7s ease;
}

.hide-header {
  top: -100px;
  position: fixed;
}

body.scrolled {
  background-color: #f0f0f0;
}

.header {
  --background-color: #ffffff;
  --heading-color: #ffffff;
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 20px 0;
  transition: all 0.5s;
  z-index: 997;
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 32px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 30px;
  margin: 0;
  font-weight: 700;
  color: var(--heading-color);
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
    margin-left: 5px;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 8px 20px;
    font-size: 14px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
    border-radius: 10px;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu a {
    font-weight: 400;
    font-size: 15px;
  }

  .navmenu li:hover > a,
  .navmenu .active,
  .navmenu .active:focus {
    color: #ababab;
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 0;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 15px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
    margin-left: 0;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover > a {
    background-color: transparent;
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown:hover > ul {
    opacity: 1;
    top: 105%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover > ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown > .dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu > ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: white;
  background-color: #181818;
  text-align: center;
  padding: 30px 0;
  position: relative;
  margin: 10px;
  border-radius: 15px;
}

.footer-items {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20px;
  padding: 20px;
  box-sizing: border-box;
}

.services-footer a {
  color: white;
}

.footer .social-links a:hover {
  color: var(--accent-color);
}

.footer .services-footer a:hover {
  color: var(--accent-color);
}

.footer .footer-quicklinks a:hover {
  color: var(--accent-color);
}

@media (max-width: 912px) {
  .footer-items {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .social-links i {
    font-size: 40px;
  }
}

.footer h3 {
  font-size: 36px;
  font-weight: 700;
  position: relative;
  padding: 0;
  margin: 0 0 15px 0;
}

.footer p {
  text-align: left;
  font-weight: 400;
  font-size: 15px;
}

.footer h4 {
  font-size: 19px;
  color: white;
  text-align: left;
}

.footer .social-links {
  margin: 20px 0 30px 0;
  gap: 10px;
}

.footer .social-links a {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  line-height: 1;
  border-radius: 10%;
  text-align: center;
  width: 36px;
  height: 36px;
  transition: 0.3s;
}

.footer .footer-quicklinks a {
  color: white;
}

.footer .footer-items i {
  font-size: 30px;
}

.footer .footer-items img {
  padding-top: 30px;
  padding-bottom: 30px;
}

.footer-items-container {
  padding-top: 40px;
}

.footer .copyright {
  padding-top: 25px;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 80%);
  font-size: 14px;
}

.footer .credits {
  font-size: 13px;
  padding-top: 5px;
}

.footer .newsletter-section h3 {
  font-size: 36px;
  font-weight: 700;
  position: relative;
  padding: 0;
  margin: 0 0 15px 0;
  text-align: left;
}

.newsletter-section form {
  display: flex;
  justify-content: left;
}

.newsletter-section input {
  padding: 10px;
  font-size: 16px;
  max-width: 300px;
}

@media (max-width: 768px) {
  .newsletter-section input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .newsletter-section button {
    padding: 10px 10px;
  }
}

.newsletter-section button {
  padding: 4.5px;
  background: none;
  color: white;
  border: none;
  cursor: pointer;
  transition: 0.4s;
}

.newsletter-section button i {
  font-size: 50px;
  transition: 0.4s;
}

.newsletter-section button:hover i {
  transform: translate(5px, 0);
  color: var(--accent-color);
}

#loading-spinner {
  display: none;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 9999;
  overflow: hidden;
  background-color: var(--background-color);
  transition: all 0.6s ease-out;
  width: 100%;
  height: 100vh;
}

#preloader:before,
#preloader:after {
  content: "";
  position: absolute;
  border: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: animate-preloader 2s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

#preloader:after {
  animation-delay: -0.5s;
}

@keyframes animate-preloader {
  0% {
    width: 10px;
    height: 10px;
    top: calc(50% - 5px);
    left: calc(50% - 5px);
    opacity: 1;
  }

  100% {
    width: 72px;
    height: 72px;
    top: calc(50% - 36px);
    left: calc(50% - 36px);
    opacity: 0;
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background: var(--button-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 160px 0 80px 0;
  text-align: center;
  position: relative;
}

.page-title:before {
  content: "";
  background-color: color-mix(
    in srgb,
    var(--background-color),
    transparent 50%
  );
  position: absolute;
  inset: 0;
}

.page-title h1 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  justify-content: center;
  padding: 0;
  margin: 0;
  font-size: 16px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li + li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li + li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 77px;
  overflow: clip;
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 14px;
  font-weight: 500;
  padding: 0;
  line-height: 1px;
  margin: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  position: relative;
}

.section-title h2::after {
  content: "";
  width: 120px;
  height: 1px;
  display: inline-block;
  background: var(--accent-color);
  margin: 4px 10px;
}

.section-title p {
  color: var(--heading-color);
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  text-transform: uppercase;
  font-family: var(--heading-font);
}

/*--------------------------------------------------------------
#Index page Section
--------------------------------------------------------------*/
@media (max-width: 912px) {
  .index-page {
    overflow-x: hidden;
  }
}

/*--------------------------------------------------------------
# banner Section
--------------------------------------------------------------*/
.banner {
  min-height: 100%;
  background-size: cover, cover;
  background-position: center, center;
  margin-top: 20px;
}

.banner-overlay h2 {
  align-items: center;
  font-weight: 600;
  font-size: 45px;
}

.banner-overlay h1 {
  align-items: center;
  font-weight: 800;
  font-size: 60px;
  margin: 0;
  color: var(--accent-color);
}

.banner-overlay .banner-button {
  display: inline-block;
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.1);
  font-weight: 600;
  background: var(--button-color);
  border-radius: 5px;
  padding: 10px 40px;
  border: 1px;
  transition: 0.4s;
  color: white;
}

.banner-overlay .banner-button:hover {
  padding: 12px 60px;
}

@media (max-width: 764px) {
  .banner-overlay h2 {
    margin-bottom: 25px;
  }

  .banner-overlay p {
    margin-top: 40px;
  }

  .banner-overlay .banner-button {
    display: flex;
    justify-content: center;
    justify-items: center;
    align-items: center;
  }
}

@media (max-width: 520px) {
  .banner-text h2 {
    font-size: 25px;
    margin-top: 20px;
  }

  .banner-text p {
    margin-top: 50px;
  }
}

.animated-text {
  height: 15vh;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 20px;
}

@media (max-width: 764px) {
  .animated-text h1 {
    font-size: 50px;
  }
}

@media (max-width: 764px) {
  .banner-text h2 {
    font-size: 35px;
  }
}

.fade-text {
  opacity: 0;
  animation: slideInFromRight 8s ease-in-out infinite;
  position: absolute;
  transform: translateX(100%); /* Start off-screen to the right */
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(100%); /* Start off-screen to the right */
  }
  25% {
    opacity: 1;
    transform: translateX(0); /* Slide into view */
  }
  50% {
    opacity: 1;
    transform: translateX(0); /* Stay in view */
  }
  75% {
    opacity: 0;
    transform: translateX(-100%); /* Slide out to the left */
  }
  100% {
    opacity: 0;
    transform: translateX(-100%); /* End off-screen to the left */
  }
}
@media (max-width: 480px) {
  .fade-text {
    width: 100%;
  }
  .fade-text h1 {
    font-size: 45px;
    margin-bottom: 30px;
    margin-top: 30px;
  }
}

.fade-text:nth-child(2) {
  animation-delay: 4s;
}

.swiper-wrapper-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.swiper-container {
  width: 100%;
  height: 100%;
}

.carousel-images .swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
}

.carousel-images .swiper-slide img {
  max-width: 100%;
  height: 450px;
  object-fit: cover;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

@keyframes bouncyNudge {
  0%,
  100% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(-40px);
  } /* Initial nudge left */
  40% {
    transform: translateX(5px);
  } /* Bounce right */
  60% {
    transform: translateX(-10px);
  } /* Bounce left */
  80% {
    transform: translateX(3px);
  } /* Bounce right */
  100% {
    transform: translateX(0);
  } /* Reset */
}

@media (max-width: 820px) {
  .banner-overlay .row {
    display: flex;
    flex-direction: column;
  }
}

.carousel-images {
  margin-top: 30px;
}

@media (max-width: 1024px) {
  .carousel-images .swiper-slide img {
    height: 400px;
    width: 100%;
    object-fit: cover;
  }

  .banner-overlay h2 {
    font-weight: 600;
    font-size: 35px;
  }

  .banner-overlay h1 {
    font-weight: 800;
    font-size: 55px;
    margin: 0;
  }
}

@media (max-width: 912px) {
  .swiper-wrapper-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .swiper-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .carousel-images .swiper-slide img {
    height: 400px;
    width: 100%;
    overflow: hidden;
  }
}

@media (max-width: 540px) {
  .carousel-images .swiper-slide img {
    height: 300px;
    width: 100%;
    object-fit: cover;
  }

  .banner-overlay h1 {
    font-weight: 700;
    font-size: 50px;
    margin: 0;
  }
}

@media (max-width: 375px) {
  .carousel-images .swiper-slide img {
    height: 250px;
    width: 100%;
    object-fit: cover;
  }

  .banner-overlay h2 {
    font-weight: 600;
    font-size: 28px;
  }

  .banner-overlay h1 {
    font-weight: 700;
    font-size: 45px;
    margin: 0;
  }
}

@media (max-width: 320px) {
  .carousel-images .swiper-slide img {
    height: 250px;
    width: 100%;
    object-fit: cover;
  }

  .banner-overlay h2 {
    font-weight: 600;
    font-size: 25px;
  }

  .banner-overlay h1 {
    font-weight: 600;
    font-size: 40px;
    margin: 0;
  }
}

/*--------------------------------------------------------------
# Sector Section
--------------------------------------------------------------*/

.sectors .sector-items {
  background-color: #252323;
  list-style: none;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  gap: 15px;
  text-align: center;
  flex-wrap: wrap;
  padding: 10px;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

.sector-item {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 10px;
  overflow: hidden;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.sectors .sector-items img {
  height: 200px;
  width: 100%;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
}

.text-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
  text-align: center;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
  z-index: 2;
}

.image-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(0.1px);
  z-index: 1;
}

.and-more-box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
}

.sector-item:hover::before {
  background: rgba(0, 0, 0, 0.7);
}

@media (max-width: 1024px) {
  .sector-item {
    position: relative;
    height: 215px;
    width: 215px;
    border-radius: 10px;
    overflow: hidden;
    object-fit: cover;
  }
}

@media (max-width: 1024px) {
  .sectors .sector-items img {
    height: 215px;
    width: 100%;
    object-fit: cover;
  }
}

@media (max-width: 764px) {
  .sector-item {
    position: relative;
    width: 100%;
    height: 150px;
    border-radius: 10px;
    overflow: hidden;
    object-fit: cover;
  }
}
/*--------------------------------------------------------------
# Edge Section
--------------------------------------------------------------*/

.edge {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.edge-items {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.edge-header {
  text-align: center;
  padding-bottom: 20px;
}

.edge-outcome {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.edge-outcome li {
  text-align: center;
  margin: 0;
  background-color: var(--surface-color);
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  height: auto;
  width: 100%;
  border-radius: 10px;
  padding: 20px;
}

.edge-outcome li h1 {
  color: var(--accent-color);
}

.edge-outcome .counter {
  font-size: 2.5rem;
  font-weight: 650;
  transition: all 4s ease-out;
}

@media screen and (max-width: 768px) {
  .edge-items {
    padding: 20px;
  }

  .edge-outcome {
    flex-direction: column;
    align-items: center;
  }

  .edge-outcome li {
    flex: 1 1 100%; /* Full width items on mobile */
    margin-bottom: 20px;
  }

  .counter {
    font-size: 2rem; /* Adjust counter font size for mobile */
  }

  .counter p {
    font-size: 0.9rem; /* Adjust paragraph font size for mobile */
  }
}

@media screen and (min-width: 768px) {
  .edge-outcome li {
    flex: 1 1 calc(50% - 20px);
  }

  .counter {
    font-size: 3rem;
  }

  .counter p {
    font-size: 1rem;
  }
}

/* Larger Screens (desktop) */
@media screen and (min-width: 1024px) {
  .edge-outcome li {
    flex: 1 1 calc(25% - 20px);
  }
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/

.services {
  background-color: var(--background2-color);
}

.services .service-item {
  border: 0.5px solid;
  border-color: #e6e6e6;
  padding: 60px 30px 30px 60px;
  transition: all ease-in-out 0.3s;
  border-radius: 5px;
  height: 380px;
  position: relative;
  overflow: hidden;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.service-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--button-color);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: -1;
}

.service-item:hover::before {
  transform: translateX(0);
}

.service-item:hover {
  background: var(--button-color);
  color: white;
}

.service-item:hover .icon i {
  color: white;
}

.service-item:hover h3,
.service-item:hover p {
  color: white;
}

.services .service-item .icon {
  position: absolute;
  left: -30px;
  top: calc(50% - 30px);
}

.services .service-item .icon i {
  font-size: 64px;
  line-height: 1;
  transition: 0.5s;
  color: var(--accent-color);
}

.services .service-item h3 {
  font-weight: 700;
  margin: 10px 0 15px 0;
  font-size: 25px;
  transition: ease-in-out 0.3s;
  text-align: justify;
}

.services .service-item p {
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 0;
}

.services .service-item span {
  color: var(--accent-color);
}

@media (min-width: 1365px) {
  .services .service-item:hover {
    transform: translateY(-10px);
  }

  .services .service-item:hover h3 {
    color: white;
  }
}

/*--------------------------------------------------------------
# Service Details Section
--------------------------------------------------------------*/

.service-details .service-box {
  position: -webkit-sticky;
  position: sticky;
  background-color: var(--surface-color);
  padding: 20px;
  box-shadow: 0px 2px 20px rgba(0, 0, 0, 0.1);
  top: 100px;
  padding-top: 10px;
  overflow-y: auto;
  max-height: calc(100vh - 40px);
  border-radius: 10px;
}

.service-details .service-box + .service-box {
  margin-top: 30px;
}

@media (max-width: 767px) {
  #service-details .row {
    display: flex;
  }
}

.service-details .service-box h4 {
  font-size: 20px;
  font-weight: 700;
  border-bottom: 2px solid
    color-mix(in srgb, var(--default-color), transparent 92%);
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.service-details .services-list {
  background-color: var(--surface-color);
}

.service-details .services-list a {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  background-color: color-mix(in srgb, var(--default-color), transparent 96%);
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-top: 15px;
  transition: 0.3s;
}

.service-details .services-list a:first-child {
  margin-top: 0;
}

.service-details .services-list a i {
  font-size: 16px;
  margin-right: 8px;
  color: var(--accent-color);
}

.service-details .services-list a.active {
  color: var(--contrast-color);
  background-color: var(--accent-color);
}

.service-details .services-list a.active i {
  color: var(--contrast-color);
}

.service-details .services-list a:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  color: var(--accent-color);
}

.service-details .download-catalog a {
  color: var(--default-color);
  display: flex;
  align-items: center;
  padding: 10px 0;
  transition: 0.3s;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.service-details .download-catalog a:first-child {
  border-top: 0;
  padding-top: 0;
}

.service-details .download-catalog a:last-child {
  padding-bottom: 0;
}

.service-details .download-catalog a i {
  font-size: 24px;
  margin-right: 8px;
  color: var(--accent-color);
}

.service-details .download-catalog a:hover {
  color: var(--accent-color);
}

.service-details .help-box {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  margin-top: 30px;
  padding: 30px 15px;
}

.service-details .help-box .help-icon {
  font-size: 48px;
}

.service-details .help-box h4,
.service-details .help-box a {
  color: var(--contrast-color);
}

.service-details .services-img {
  margin-bottom: 20px;
}

.service-details h3 {
  font-size: 26px;
  font-weight: 700;
}

.service-details p {
  font-size: 15px;
}

.service-details ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.extras-items {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  padding: 0;
  list-style-type: none;
  margin-bottom: 100px;
}

.extras {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  background-color: rgb(242, 242, 251);
  border-radius: 10px;
}

@media (max-width: 768px) {
  .extras {
    grid-template-columns: 1fr;
  }
}

.extras li {
  margin: 5px;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  background-color: white;
  align-items: center;
  height: auto;
  border-radius: 10px;
  padding: 10px;
}

.extras li i {
  color: var(--accent-color);
  font-size: 25px;
}

.service-details .extras i {
  margin-right: 8px;
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about-homepage {
  background: #e0e0e0;
  border-radius: 30px;
  padding: 30px;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
}

.about-homepage .about-content p {
  color: white;
}

.about ul {
  list-style: none;
  padding: 0;
}

.about-homepage img {
  max-width: 100%;
  height: 500px;
  border-radius: 10px;
  object-fit: cover;
}

.about .read-more {
  border: 1px solid black;
  font-family: var(--heading-font);
  font-weight: 600;
  letter-spacing: 1px;
  padding: 10px 32px;
  border-radius: 5px;
  transition: 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--default-color);
}

.read-more i {
  color: #ffffff;
}

.about .read-more i {
  font-size: 18px;
  margin-left: 5px;
  line-height: 0;
  transition: 0.3s;
  color: #060606;
}

.about .read-more:hover {
  background: var(--button-color);
  color: white;
  padding: 12px 40px;
  border: none;
}

.about .read-more:hover i {
  transform: translate(5px, 0);
  color: white;
}

@media (max-width: 1024px) {
  .about-homepage img {
    max-width: 100%;
    height: 400px;
    border-radius: 10px;
    object-fit: cover;
  }
}

@media (max-width: 912px) {
  .about-homepage img {
    max-width: 100%;
    height: 500px;
    border-radius: 10px;
    object-fit: cover;
  }
}

@media (max-width: 540px) {
  .about-homepage {
    padding: 10px;
    border-radius: 0;
    box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  }

  .about-homepage img {
    max-width: 100%;
    height: 300px;
    border-radius: 10px;
    object-fit: cover;
  }

  .about .read-more {
    display: flex;
    align-items: center;
  }
}

/*--------------------------------------------------------------
# Stack Section
--------------------------------------------------------------*/
.features {
  background-color: var(--background2-color);
}

.stack-container h1 {
  font-weight: 700;
  font-size: 45px;
}

.stack-items {
  width: 100%;
  display: grid;
  flex-wrap: wrap;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: auto;
  list-style-type: none;
  position: relative;
  overflow: hidden;
  gap: 10px;
}

.stack-items li {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 1rem;
  background-color: white;
  height: 80px;
  width: 80px;
  transition: transform 0.3s ease;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
}

.stack-items li:hover {
  transform: translateY(-5px);
}

.stack-items img {
  height: 60px;
}

.features .tab-content {
  margin-top: 30px;
}

.features .tab-pane h3 {
  color: var(--heading-color);
  font-weight: 600;
  font-size: 24px;
}

.features .tab-pane ul {
  list-style: none;
  padding: 4%;
}

.features .tab-pane ul li {
  padding-bottom: 5px;
}

.features .tab-pane ul i {
  font-size: 20px;
  padding-right: 4px;
  color: var(--accent-color);
}

.features .tab-pane p:last-child {
  margin-bottom: 0;
}

.stack-btn {
  padding-top: 30px;
}

.stack-btn span {
  display: inline-block;
  background: var(--button-color);
  border-radius: 5px;
  padding: 10px 32px;
  color: #ffffff;
  transition: ease 0.3s;
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.stack-btn span:hover {
  padding: 12px 50px;
}

@media (max-width: 1024px) {
  .stack-items {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 912px) {
  .stack-items {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 540px) {
  .stack-items {
    grid-template-columns: repeat(4, 1fr);
  }

  .stack-btn span {
    display: flex;
    justify-content: center;
    justify-items: center;
    align-items: center;
  }
}

@media (max-width: 412px) {
  .stack-items li {
    height: 70px;
    width: 100%;
  }

  .stack-items img {
    height: 50px;
  }
}

@media (max-width: 414px) {
  .stack-items {
    justify-content: center;
  }
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/

.testimonials .testimonial-item {
  background-color: white;
  box-sizing: content-box;
  padding: 30px;
  margin: 30px 15px;
  position: relative;
  height: 400px;
  max-width: 100%;
  gap: 20px;
  border: solid 1px;
  border-color: gainsboro;
  border-radius: 5px;
}

.testimonials .testimonial-item .testimonial-img {
  width: 90px;
  border-radius: 50px;
  margin-right: 15px;
}

.testimonials .testimonial-item h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 5px 0;
}

.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 0;
}

.testimonials .testimonial-item .stars {
  margin: 10px 0;
}

.testimonials .testimonial-item .stars span {
  color: goldenrod;
  margin: 0 1px;
  font-size: 20px;
}

.testimonials .testimonial-item .quote-icon-left,
.testimonials .testimonial-item .quote-icon-right {
  color: color-mix(in srgb, black, transparent 50%);
  font-size: 26px;
  line-height: 0;
}

.testimonials .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}

.testimonials .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
  transform: scale(-1, -1);
}

.testimonials .testimonial-item p {
  font-style: italic;
  margin: 15px auto 15px auto;
}

.testimonials .swiper-wrapper {
  height: auto;
}

.testimonials .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: color-mix(in srgb, var(--default-color), transparent 85%);
  opacity: 1;
}

.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

@media (max-width: 767px) {
  .testimonials .testimonial-wrap {
    padding-left: 0;
  }

  .testimonials .testimonial-item {
    padding: 30px;
    margin: 15px;
  }

  .testimonials .testimonial-item .testimonial-img {
    position: static;
    left: auto;
  }
}

/*--------------------------------------------------------------
# Team Section
--------------------------------------------------------------*/
.team .team-member {
  background-color: var(--surface-color);
  overflow: hidden;
  border-radius: 5px;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  height: 100%;
  width: 100%;
}

.team .team-member .member-img {
  position: relative;
  overflow: hidden;
}

.team .team-member .member-img img {
  height: 350px;
  width: 100%;
  object-fit: cover;
}

.team .team-member .social {
  position: absolute;
  left: 0;
  bottom: 30px;
  right: 0;
  transition: ease-in-out 0.3s;
  text-align: center;
}

.team .team-member .social a {
  background: color-mix(in srgb, var(--contrast-color), transparent 25%);
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0 3px;
  border-radius: 4px;
  width: 36px;
  height: 36px;
  transition: ease-in-out 0.3s;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.team .team-member .social a:hover {
  color: var(--contrast-color);
  background: var(--accent-color);
}

.team .team-member .social i {
  font-size: 18px;
  line-height: 0;
}

.team .team-member .member-info {
  padding: 25px 15px;
}

.team .team-member .member-info h4 {
  font-weight: 700;
  margin-bottom: 5px;
  font-size: 18px;
}

.team .team-member .member-info span {
  display: block;
  font-size: 13px;
  font-weight: 400;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.team .team-member:hover .social {
  opacity: 1;
  bottom: 15px;
}
/*--------------------------------------------------------------
# Project Section
--------------------------------------------------------------*/
.project-base li a {
  display: inline-block;
  background: var(--button-color);
  color: #ffffff;
  border: 1px;
  border-radius: 5px;
  margin-left: 1px;
  transition: ease 0.5s;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.project-base li a:hover {
  color: white;
  padding: 10px 40px;
}

.project {
  background-color: var(--background2-color);
  width: 100%;
  margin-top: 10px;
}

.container.project-items {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.project-message,
.django-form {
  flex: 1;
  min-width: 300px;
}

.project-message h1 {
  font-weight: 700;
  margin-bottom: 30px;
}

.django-form {
  min-height: 100%;
  transition: all ease-in-out 0.3s;
  border: 1px solid;
  border-color: gainsboro;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  padding-top: 50px;
  padding-bottom: 50px;
  padding-left: 20px;
  padding-right: 20px;
  background-color: white;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
}

.project .django-form button {
  padding: 10px 20px;
  font-size: 16px;
  background: var(--button-color);
  color: white;
  border: 1px;
  border-radius: 5px;
  cursor: pointer;
  transition: ease 0.5s;
}

.project .django-form button:hover {
  background: color-mix(in srgb, var(--link-color), transparent 20%);
}

@media (max-width: 768px) {
  .container.project-items {
    flex-direction: column;
    align-items: center;
  }

  .project-message,
  .django-form {
    min-width: unset;
  }

  .django-form {
    min-width: unset;
    min-height: 100%;
  }
}

@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact {
  padding: 0;
  width: 100%;
}

.contact .contact-page-image img {
  height: 600px;
  width: 100%;
  border-radius: 50%;
  object-fit: cover;
  padding: 40px;
  margin-top: 30px;
}

.contact .contact-page-address {
  text-align: left;
  width: 100%;
  padding: 40px;
}

.contact .address-items {
  display: flex;
  justify-items: center;
}

.contact .address-items i {
  padding-right: 10px;
  font-size: 18px;
}

.contact .address-items p {
  font-size: 17px;
}

@media (max-width: 480px) {
  .contact .contact-page-image img {
    width: 100%;
    height: 400px;
    border-radius: 50%;
    object-fit: cover;
    padding: 10px;
    margin-bottom: 40px;
  }

  .contact .contact-page-address {
    text-align: left;
    width: 100%;
    padding: 10px;
  }
}

@media (max-width: 375px) {
  .contact .contact-page-image img {
    width: 100%;
    height: 350px;
    border-radius: 50%;
    object-fit: cover;
    padding: 10px;
    margin-bottom: 40px;
  }
}

/*--------------------------------------------------------------
# Pages Section
--------------------------------------------------------------*/
.pages {
  height: 100%;
}

.pages-items {
  margin-left: 10%;
  margin-right: 10%;
}

@media (max-width: 912px) {
  .pages-items {
    margin-left: 3%;
    margin-right: 3%;
  }
}

/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
.widgets-container {
  margin: 60px 0 30px 0;
}

.widget-title {
  color: var(--heading-color);
  font-size: 20px;
  font-weight: 600;
  padding: 0 0 0 10px;
  margin: 0 0 20px 0;
  border-left: 4px solid var(--accent-color);
}

.widget-item {
  margin-bottom: 30px;
  background-color: color-mix(in srgb, var(--default-color), transparent 98%);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  padding: 30px;
  border-radius: 5px;
}

.widget-item:last-child {
  margin-bottom: 0;
}

.blog-author-widget img {
  max-width: 160px;
}

.blog-author-widget h4 {
  font-weight: 600;
  font-size: 24px;
  margin: 15px 0 0 0;
  padding: 0;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.blog-author-widget .social-links {
  margin: 5px 0;
}

.blog-author-widget .social-links a {
  color: color-mix(in srgb, var(--default-color), transparent 60%);
  margin: 0 3px;
  font-size: 18px;
}

.blog-author-widget .social-links a:hover {
  color: var(--accent-color);
}

.blog-author-widget p {
  font-style: italic;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin: 10px 0 0 0;
}

.search-widget form {
  background: var(--background-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 75%);
  padding: 3px 10px;
  position: relative;
  border-radius: 50px;
  transition: 0.3s;
}

.search-widget form input[type="text"] {
  border: 0;
  padding: 4px 10px;
  border-radius: 4px;
  width: calc(100% - 40px);
  background-color: var(--background-color);
  color: var(--default-color);
}

.search-widget form input[type="text"]:focus {
  outline: none;
}

.search-widget form button {
  background: none;
  color: var(--default-color);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  border: 0;
  font-size: 16px;
  padding: 0 16px;
  transition: 0.3s;
  line-height: 0;
}

.search-widget form button i {
  line-height: 0;
}

.search-widget form button:hover {
  color: var(--accent-color);
}

.search-widget form:is(:focus-within) {
  border-color: var(--accent-color);
}

.categories-widget ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.categories-widget ul li {
  padding-bottom: 10px;
}

.categories-widget ul li:last-child {
  padding-bottom: 0;
}

.categories-widget ul a {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  transition: 0.3s;
}

.categories-widget ul a:hover {
  color: var(--accent-color);
}

.categories-widget ul a span {
  padding-left: 5px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 14px;
}

.recent-posts-widget .post-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.recent-posts-widget .post-item:last-child {
  margin-bottom: 0;
}

.recent-posts-widget .post-item h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.recent-posts-widget .post-item h4 a {
  color: var(--default-color);
  transition: 0.3s;
}

.recent-posts-widget .post-item h4 a:hover {
  color: var(--accent-color);
}

.recent-posts-widget .post-item time {
  display: block;
  font-style: italic;
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

.tags-widget ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tags-widget ul li {
  display: inline-block;
}

.tags-widget ul a {
  background-color: color-mix(in srgb, var(--default-color), transparent 94%);
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  border-radius: 50px;
  font-size: 14px;
  padding: 5px 15px;
  margin: 0 6px 8px 0;
  display: inline-block;
  transition: 0.3s;
}

.tags-widget ul a:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.tags-widget ul a span {
  padding-left: 5px;
  color: color-mix(in srgb, var(--default-color), transparent 60%);
  font-size: 14px;
}

@media (max-width: 768px) {
  .career-table {
    display: flex;
  }
}
