/* Main container */
.detail-gallery {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  padding-bottom: 20px;
}

.single-image img {
  aspect-ratio: 16/9;
  height: 100%;
  width: 100%;
  border-radius: 10px;
  object-fit: cover;
}

.product-image-slider {
  border-radius: 10px;
}

.product-image-slider figure {
  width: 100%;
  aspect-ratio: 16/9;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 10px;
}

.product-image-slider img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  display: none;
}

.product-image-slider img.active {
  display: block;
}

/* Thumbnails */
.slider-nav-thumbnails {
  display: flex;
  overflow: hidden;
  gap: 0;
  padding: 0;
  margin: 0;
  border-radius: 20px;
}

.slider-nav-thumbnails img {
  width: 100%;
  height: 100px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  padding: 0;
  margin: 0;
}

.slider-nav-thumbnails img:hover {
  transform: scale(1.05);
}

.slider-nav-thumbnails img.active {
  opacity: 0.6;
}

@media (max-width: 820px) {
  .product-image-slider figure {
    height: 500px;
    width: 100%;
    aspect-ratio: 16/9;
  }

  .product-image-slider img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .slider-nav-thumbnails img {
    height: 80px;
  }

  .single-image img {
    height: 500px;
    width: 100%;
    border-radius: 10px;
    object-fit: cover;
  }
}

@media (max-width: 540px) {
  .product-image-slider figure {
    height: 240px;
    width: 100%;
    aspect-ratio: 16/9;
  }

  .single-image img {
    height: 240px;
    width: 100%;
    border-radius: 10px;
    object-fit: cover;
  }
}

@media (max-width: 480px) {
  .slider-nav-thumbnails img {
    height: 60px;
  }

  .slider-nav-thumbnails {
    gap: 0;
  }
}
