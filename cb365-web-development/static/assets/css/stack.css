/*Stack*/
.stack-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.stack-page-items {
  display: flex;
  grid-template-columns: 2, 1fr;
  justify-content: space-between;
  margin-bottom: 40px;
}

.stack-page-header h1 {
  font-weight: 600;
  margin: 0;
  padding-right: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

th,
td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
  word-wrap: break-word;
}

tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

@media screen and (max-width: 912px) {
  .stack-page-items {
    flex-direction: column;
    padding: 10px;
  }

  .stack-page-header h1 {
    font-size: 35px;
    padding-right: 0;
  }

  table {
    width: 100%;
    margin-bottom: 20px;
  }

  th,
  td {
    font-size: 12px;
    padding: 8px;
  }
}

@media screen and (max-width: 512px) {
  .stack-page-header h1 {
    font-size: 30px;
  }

  th,
  td {
    font-size: 10px;
    padding: 6px;
  }
}
