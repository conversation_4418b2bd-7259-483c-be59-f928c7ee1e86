(function ($) {
  "use strict";

  /*Product Details*/
  var productDetails = function () {
    // Initialize main image slider
    $(".product-image-slider").slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: false,
      fade: false,
      asNavFor: ".slider-nav-thumbnails",
    });

    // Initialize thumbnail slider with responsive settings
    $(".slider-nav-thumbnails").slick({
      slidesToShow: 4, // Default for larger screens
      slidesToScroll: 1,
      asNavFor: ".product-image-slider",
      dots: false,
      focusOnSelect: true,
      prevArrow:
        '<button type="button" class="slick-prev"><i class="fi-rs-arrow-small-left"></i></button>',
      nextArrow:
        '<button type="button" class="slick-next"><i class="fi-rs-arrow-small-right"></i></button>',
      responsive: [
        {
          breakpoint: 768, // For tablets and smaller
          settings: {
            slidesToShow: 3,
          },
        },
        {
          breakpoint: 480, // For mobile devices
          settings: {
            slidesToShow: 2,
          },
        },
      ],
    });

    // Remove active class from all thumbnail slides
    $(".slider-nav-thumbnails .slick-slide").removeClass("slick-active");

    // Set active class to the first thumbnail slide
    $(".slider-nav-thumbnails .slick-slide").eq(0).addClass("slick-active");

    // Change active thumbnail when the main slide changes
    $(".product-image-slider").on(
      "beforeChange",
      function (event, slick, currentSlide, nextSlide) {
        var mySlideNumber = nextSlide;
        $(".slider-nav-thumbnails .slick-slide").removeClass("slick-active");
        $(".slider-nav-thumbnails .slick-slide")
          .eq(mySlideNumber)
          .addClass("slick-active");
      }
    );

    // Optionally, remove zoom feature completely on smaller screens (below 768px)
    if ($(window).width() <= 768) {
      $(".product-image-slider img").each(function () {
        $(this).off("mouseenter");
      });
    }
  };

  // Load functions after the document is ready
  $(document).ready(function () {
    productDetails();
  });

  // Reinitialize productDetails on window resize
  $(window).on("resize", function () {
    productDetails(); // Ensure proper slider and zoom handling on window resize
  });
})(jQuery);
