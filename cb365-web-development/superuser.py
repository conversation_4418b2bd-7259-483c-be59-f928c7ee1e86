import os
import django
from dotenv import load_dotenv

load_dotenv()

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cobber.settings')
django.setup()

from django.contrib.auth.models import User

def create_superuser(username, email, password):
    if User.objects.filter(username=username).exists():
        print('Superuser already exists')
    else:
        User.objects.create_superuser(username=username, email=email, password=password)
        print('Superuser created successfully')

if __name__ == '__main__':

    username = os.getenv('SUPERUSER_USERNAME')
    email = os.getenv('SUPERUSER_EMAIL')
    password = os.getenv('SUPERUSER_PASSWORD')

    if username and email and password:
        create_superuser(username, email, password)
    else:
        print("Missing one or more environment variables: SUPERUSER_USERNAME, SUPERUSER_EMAIL, SUPERUSER_PASSWORD")
