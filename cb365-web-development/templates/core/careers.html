{% extends 'partials/base.html'%}
{%load static %}
{% block content %}
<main class="main">
  {%for image in careers%}
  {% if image %}
  <style>
      .breadcrumbs-image {
          background-image: url('{{ image.breadcrumbs_image.url }}');
          background-size: cover;
          background-position: center;
          top: 0;
          left: 0;
          width: 100%;
          height: 350px; 
          z-index: -2; 
          overflow: hidden; 
          
      }
      .breadcrumbs-image::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.2);
          backdrop-filter: blur(0.1px);
          z-index: -1;
        }   
        @media (max-width:1024px){
          .breadcrumbs-image {
          height: 300px; 
          }
        }
  </style>    
  {%endif%}
  {% endfor %}

    <!-- Page Title -->
    <div class="page-title breadcrumbs-image">
      <div class="container position-relative">
        <h1>{{careers_plural_name}} </h1>
      </div>
    </div><!-- End Page Title -->

    <!-- Starter Section Section -->
    <section id="careers" class="careers section">
      {%if careers%}
      <div class="container" data-aos="fade-up">
        <div>
          {%for job in careers %}
          <div class="careers-job-list">
            <a href="">
              <h4>{{job.title}}</h4>
              <p><span>Location: </span>{{job.location}} </p>
            </a> 
          </div>
          {%endfor%}
        </div>
        <hr>
      </div>
      {%else%}
      <div class="job-unavailable">
        <i class="bi bi-building"></i>
        <p>No Jobs Available</p>
      </div>
      {%endif%}
    </section><!-- /Starter Section Section -->

  </main>

{%endblock content%}