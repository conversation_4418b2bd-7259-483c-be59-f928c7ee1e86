{% extends "partials/base.html" %}
{% load static %}

{% block content %}
<main>
    {% for breadcrumbs in contact %}
    {% if breadcrumbs.breadcrumbs_image %}
    <style>
        .breadcrumbs-image {
            background-image: url('{{ breadcrumbs.breadcrumbs_image.url }}');
            background-size: cover;
            background-position: center;
            top: 0;
            left: 0;
            width: 100%;
            height: 350px; 
            z-index: -2; 
            overflow: hidden; 
            
        }
        .breadcrumbs-image::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(0.1px);
            z-index: -1;
          }   
          @media (max-width:1024px){
            .breadcrumbs-image {
            height: 300px; 
            }
          }
    </style>    
    {%endif%}
    {%endfor%}
  
      <!-- Page Title -->
      <div class="page-title breadcrumbs-image">
        <div class="container position-relative ">
          <h1 style="color: white;"> {{contact_plural_name}} </h1>
        </div>
      </div><!-- End Page Title -->


      <section class=" section contact" data-aos="fade-up" data-aos-delay="100">
        <div class="container">
            <div class="row d-flex align-items-center justify-content-center">
                <div class="col-lg-6">
                    {% for contact_page in contact%}
                    <div class="contact-page-image">
                        <img src="{{contact_page.image.url}}" alt="" class="img-fluid wow fadeInUp">
                    </div>
                    {% endfor %}
                </div>
                
                <div class="col-lg-6">
                    {% for address in contact_address %}
                    <div class="contact-page-address">
                        
                    <div>
                        <h2>{{address.country}} </h2>
                    </div>
    
                    <div class="address-items">
                        <div >
                            <i class="bi bi-geo-alt-fill flex-shrink-0"></i>
                        </div>
                        <div>
                            <p>{{address.address}} </p>
                        </div>
                    </div>
    
                    <div class="address-items">
                        <div>
                            <i class="bi bi-phone-fill flex-shrink-0"></i>
                        </div>
                        <div>
                            <p>{{address.phone}} </p>
                        </div>
                    </div>
    
                    <div class="address-items">
                        <div>
                            <i class="bi bi-envelope-fill flex-shrink-0"></i>
                        </div>
                        <div>
                            <p>{{address.email}} </p>
                        </div>
                    </div>
                    
                    </div>
                    {%endfor%}
                </div>
            </div>
        </div>
      </section>
</main>
{% endblock content %}