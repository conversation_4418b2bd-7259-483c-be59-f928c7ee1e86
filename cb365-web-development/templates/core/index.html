{%extends 'partials/base.html' %}
{% load static %}
{% block content %}
<main class="main index-page">

<!-- banner section start -->
  <section class="banner section" data-scroll-index='0'>
    <div class="banner-overlay">
      <div class="container">
        {% for hero in hero_section %}
        <div class="row banner-items d-flex align-items-center justify-content-center">
          <div class="col-lg-6">
            <div class="banner-text" data-aos="fade-up" data-aos-delay="100">
              <h2 class="white">{{hero.title}} </h2>
              <div class="animated-text">
                {% for title in animated_title %}
                  {% if title %}
                  <div  class="fade-text"><h1>{{ title.title }}</h1></div>
                    
                  {% endif %}
                {% endfor %}
              </div>
              <p class="animate__animated animate__fadeInDown" style="font-weight: 500;">{{ hero.description }}</p>
              {% if hero.button %}
              <a href="{% url 'mail_and_forms:project' %}" class="banner-button">{{ hero.button }}</a>
              {% endif %}
            </div>
          </div>
  
          <!-- Carousel Section -->
          <div class="col-lg-6 carousel-images" data-aos="fade-up" data-aos-delay="100">
            <div class="swiper-wrapper-container" style="position: relative;">
              <div class="swiper-container">
                <div class="swiper-wrapper">
                  {%for image in carousel_images%}
                  {% if image %}
                  <div class="swiper-slide">
                      <img src="{{ image.images.url }}" alt="" class="img-fluid wow fadeInUp">            
                  </div>
                  {% endif %}
                  {%endfor%}
                  
                </div>
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        const swiper = new Swiper('.swiper-container', {
          loop: false,
          
        });
      });

      const carousel = document.querySelector('.carousel-images'); 
      const slides = document.querySelectorAll('.carousel-images .swiper-slide');
      let nudgeInterval;
      let inactivityTimer;

     
      function triggerBouncyNudge() {
        slides.forEach(slide => {
          slide.style.animation = 'none'; 
          void slide.offsetWidth; 
          slide.style.animation = 'bouncyNudge 1s ease-in-out 1'; 
        });
      }

      function startNudgeInterval() {
        nudgeInterval = setInterval(triggerBouncyNudge, 10000);
      }

      // Stop nudging on interaction
      function stopNudging() {
        clearInterval(nudgeInterval); // Stop the auto-nudge
        clearTimeout(inactivityTimer); // Clear any pending resume
      }

      // Resume nudging after inactivity (e.g., 10 seconds)
      function resumeAfterInactivity() {
        inactivityTimer = setTimeout(startNudgeInterval, 20000); 
      }

      // Initialize auto-nudging
      startNudgeInterval();

      // ===== Interaction Listeners =====
      // 1. Mouse Enter (hover over carousel)
      carousel.addEventListener('mouseenter', () => {
        stopNudging();
      });

      // 2. Mouse Leave (resume after delay)
      carousel.addEventListener('mouseleave', () => {
        resumeAfterInactivity();
      });

      // 3. Mouse Move (user is actively interacting)
      let isMouseMoving = false;
      let mouseMoveTimer;

      carousel.addEventListener('mousemove', () => {
        if (!isMouseMoving) {
          stopNudging(); // Stop on first interaction
          isMouseMoving = true;
        }
        
        // Debounce to avoid constant stops
        clearTimeout(mouseMoveTimer);
        mouseMoveTimer = setTimeout(() => {
          isMouseMoving = false;
          resumeAfterInactivity(); // Resume after mouse stops
        }, 2000); // Adjust debounce delay (2s)
      });

      // 4. For Swiper.js (if you're using it)
      const swiper = new Swiper('.swiper', {
        on: {
          touchStart: stopNudging, // Stop on touch/swipe
          slideChange: stopNudging, // Stop on slide change
        },
      });
    </script>

  </section>

<!-- sector section start -->
  <section class="section">
    <div class="container" data-aos="fade-up" data-aos-delay="100">
      {% for sector in sectors %}
      <div class="section-title">
        <h2>Sectors</h2>
        <p>{{ sector.title }}</p>
      </div>
      {% endfor %}
      <div class="sectors">
        <ul class="sector-items">
          {% for sectortypes in sector_types %}
            {% if sectortypes %}
              <li class="sector-item">
                <div class="image-container">
                  <img src="{{ sectortypes.image.url }}" alt="{{ sectortypes.name }}">
                  <div class="text-overlay">{{ sectortypes.name }}</div>
                </div>
              </li>
            {% endif %}
          {% endfor %}
          <li class="sector-item">
            <div class="and-more-box">
              <p>And More</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </section>

<!-- service section start -->
  <section id="services" class="services section">

    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
      <h2>Services</h2>
      <p>What we do offer</p>
    </div><!-- End Section Title -->

    <div class="container">

      <div class="row gy-4">
        {%for svc in services%}
        <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
          <div class="service-item  position-relative">
            <div class="icon">
            </div>
            <a href="{%url 'services_and_more:service' svc.slug %}" class="stretched-link">
              <h3>{{svc.title}}</h3>
            </a>
            <p>{{svc.description}} </p>
            <span>Read More →</span>
          </div>
        </div><!-- End Service Item -->
        {%endfor%}


      </div>

    </div>

  </section>

<!-- stack section start -->
  <section id="features" class="features section background-class conatainer">

     <!-- Section Title -->
     <div class="container section-title" data-aos="fade-up">
      <h2>Stack</h2>
    </div><!-- End Section Title -->

    <div class="container">
      <div class="tab-content " data-aos="fade-up" data-aos-delay="200">
        <div class="tab-pane fade active show " id="features-tab-1">
          <div class="row stack-container">
            <div class="col-lg-6 order-2 order-lg-1 mt-3 mt-lg-0">
              {%for file in stack_homepage %}
              <h1>{{file.description}} </h1>
              <div class="stack-btn">
                <a href="{% url 'services_and_more:stack' %}"><span> {{file.button}} </span></a>  
              </div>
                            
              {%endfor%}
            </div>
            
            <div class="col-lg-6 order-1 order-lg-2 text-center container ">
              <ul class="stack-items">  
                    {% for images in stack_homepage_images.all %}
                        <li>
                            <img src="{{ images.image.url }}" alt="None" class="img-fluid inline-block" >
                        </li>
                    {% endfor %}
              </ul>
            </div>
          </div>
        </div><!-- End Tab Content Item -->
      </div>
    </div>
  </section>

<!-- testimonials section start -->
{% if testimonials %}
<section id="testimonials" class="testimonials section">

    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
      <h2>Testimonials</h2>
      {% for tes in testimonials %}
      <p>{{tes.description}} </p>
      {%endfor%}
    </div><!-- End Section Title -->

    <div class="container" data-aos="fade-up" data-aos-delay="100">

      <div class="swiper init-swiper">
        <script type="application/json" class="swiper-config">
          {
            "loop": true,
            "speed": 600,
            "autoplay": {
              "delay": 5000
            },
            "slidesPerView": "auto",
            "pagination": {
              "el": ".swiper-pagination",
              "type": "bullets",
              "clickable": true
            },
            "breakpoints": {
              "320": {
                "slidesPerView": 1,
                "spaceBetween": 40
              },
              "1200": {
                "slidesPerView": 4,
                "spaceBetween": 10
              }
            }
          }
        </script>
        <div class="swiper-wrapper  ">

          {%for client in testimonial_list %}
          <div class="swiper-slide ">
            
            <div class="testimonial-item">
              <h3>{{client.name}}</h3>
              <div class="stars">
                <span>{{ client.get_rating_display }}</span>
              </div>
              <p>
                <i class="bi bi-quote quote-icon-left"></i>
                <span>{{client.message}}</span>
                <i class="bi bi-quote quote-icon-right"></i>
              </p>
            </div>
            
          </div><!-- End testimonial item -->
          {%endfor%}

        </div>
        <div class="swiper-pagination"></div>
      </div>

    </div>

</section>
{%endif%}
  
<!-- about us section start -->
  <section id="about" class="about section">
     <!-- Section Title -->
     <div class="container section-title" data-aos="fade-up">
      <h2>About Us</h2>
    </div><!-- End Section Title -->

    <div class="container about-homepage">
      <div class="row gy-4">
        <div class="col-lg-6" data-aos="fade-up" data-aos-delay="200">
          {%if homepage_about_us.about_image %}
          <img src="{{homepage_about_us.about_image.url}}" alt="" class="img-fluid"> 
          {%endif%}
        </div>

        <div class="col-lg-6 about-content" data-aos="fade-up" data-aos-delay="100">
          <p">    
            {{ homepage_about_us.about|safe|truncatechars:1000 }}
          </p>
          {%if homepage_about_us.slug%}
          <a href="{% url 'cobber365:about-us' homepage_about_us.slug %}" class="read-more"><span> {{homepage_about_us.read_more}} </span><i class="bi bi-arrow-right"></i></a>
          {%endif%}
        </div>
      </div>
    </div>

  </section>


</main>
{% endblock content %}