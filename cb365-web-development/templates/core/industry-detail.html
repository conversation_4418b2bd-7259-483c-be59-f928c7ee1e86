{% extends 'partials/base.html' %}
{% load static %}
{% block content %}
<main class="main">
  {% for image in services %}
  {% if image %}
  
      <style>
          .breadcrumbs-image {
              background-image: url('{{ image.image.url }}');
              background-size: cover;
              background-position: center;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%; 
              z-index: -1; 
              overflow: hidden; 
              
          }

          
      </style>
      
  {%endif%}
  {% endfor %}

  <!-- Page Title -->
  <div class="page-title dark-background">
    <div class="container position-relative breadcrumbs-image">
      <h1>{{industry.title}} </h1>
    </div>
  </div><!-- End Page Title -->

  <div class="container">
    <div class="row">

      <div class="col-lg-8">

        <!-- Industry Details Section -->
        <section id="blog-details" class="blog-details section">
          <div class="container">

            <article class="article">
              <div class="content">
                <p>
                  {{industry.body|safe}}
                </p>
                
                <!-- Industry List -->
                <hr>
                 {% for list in industry_list %}
                <h2 style="font-weight: 700;">{{list.title}} </h2>
                <img src="{{list.image.url}}" class="img-fluid" alt="">
                <p>
                  {{list.body|safe}}
                </p>
                <hr>
                {%endfor%}     
              </div>
            </article>
          </div>
        </section>
      </div>
      
      <div class="col-lg-4 sidebar">
        <div class="widgets-container">
          <!-- Industry Widget -->
          <div class="categories-widget widget-item">
            <h3 class="widget-title">Industry List</h3>
            <ul class="mt-3">
              {%for industry in industrys%}
              <li><a href="{% url 'src:industry-detail' industry_slug=industry.slug %}" style="font-weight: 700;">{{industry.title}} </a></li>
              <hr>
              {%endfor%}
            </ul>
          </div><!--/Industry Widget -->
        </div>
      </div>
    </div>
  </div>

</main>
{%endblock content%}