{% extends 'partials/base.html' %}
{% load static %}
{% block content %}
<main class="main">

  {% if team.breadcrumbs_image %}
  <style>
      .breadcrumbs-image {
          background-image: url('{{ team.breadcrumbs_image.url }}');
          background-size: cover;
          background-position: center;
          top: 0;
          left: 0;
          width: 100%;
          height: 350px; 
          z-index: -2; 
          overflow: hidden; 
          
      }
      .breadcrumbs-image::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.2);
          backdrop-filter: blur(0.1px);
          z-index: -1;
        }   
        @media (max-width:1024px){
          .breadcrumbs-image {
          height: 300px; 
          }
        }
  </style>    
  {%endif%}

    <!-- Page Title -->
    <div class="page-title breadcrumbs-image">
      <div class="container position-relative ">
        <h1 style="color: white;"> {{team_plural_name}} </h1>
      </div>
    </div><!-- End Page Title -->

  <!-- Team Section Section -->
  <section id="team" class="team section">
      <div data-aos="fade-up">
        {%if team.image%}
        <div class="team-image-container">
          <img src="{{team.image.url}}" alt="">
        </div>
        {%endif%}
        <div class="team-text container">
          <p>{{team.text|safe}} </p>
        </div>
      </div>

      <div class="container" data-aos="fade-up">
        <div class="container">
          <div class="row gy-4 team-view">
            {%for members in team_members %}
            <div class="col-lg-3 col-md-6 d-flex align-items-stretch" data-aos="fade-up" data-aos-delay="100">
              <div class="team-member">
                <div class="member-img">
                  <img src="{{members.image.url}}" class="img-fluid" alt="">
                  <div class="social">
                    {% if members.x %}
                    <a href="{{members.x}}"><i class="bi bi-twitter-x"></i></a>
                    {%endif%}
                    {% if members.linkedin %}
                    <a href="{{members.linkedin}}"><i class="bi bi-linkedin"></i></a>
                    {%endif%}
                  </div>
                </div>
                <div class="member-info">
                  <h4>{{members.name}} </h4>
                  <span>{{members.position}} </span>
                </div>
              </div>
            </div><!-- End Team Member -->
            {%endfor%}
          </div>
        </div>


      </div>
  </section><!-- /Team Section Section -->


<!-- testimonials section start -->
{% if testimonials %}
<section id="testimonials" class="testimonials section">

    <!-- Section Title -->
    <div class="container section-title" data-aos="fade-up">
      <h2>Testimonials</h2>
      {% for tes in testimonials %}
      <p>{{tes.description}} </p>
      {%endfor%}
    </div><!-- End Section Title -->

    <div class="container" data-aos="fade-up" data-aos-delay="100">

      <div class="swiper init-swiper">
        <script type="application/json" class="swiper-config">
          {
            "loop": true,
            "speed": 600,
            "autoplay": {
              "delay": 5000
            },
            "slidesPerView": "auto",
            "pagination": {
              "el": ".swiper-pagination",
              "type": "bullets",
              "clickable": true
            },
            "breakpoints": {
              "320": {
                "slidesPerView": 1,
                "spaceBetween": 40
              },
              "1200": {
                "slidesPerView": 4,
                "spaceBetween": 10
              }
            }
          }
        </script>
        <div class="swiper-wrapper  ">

          {%for client in testimonial_list %}
          <div class="swiper-slide ">
            
            <div class="testimonial-item">
              <h3>{{client.name}}</h3>
              <div class="stars">
                <span>{{ client.get_rating_display }}</span>
              </div>
              <p>
                <i class="bi bi-quote quote-icon-left"></i>
                <span>{{client.message}}</span>
                <i class="bi bi-quote quote-icon-right"></i>
              </p>
            </div>
            
          </div><!-- End testimonial item -->
          {%endfor%}

        </div>
        <div class="swiper-pagination"></div>
      </div>

    </div>

</section>
{%endif%}



  </main>
{%endblock content%}