
<!DOCTYPE html>
<html lang="en">
<head>
    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partnership</title>
  <!-- Favicons -->
  <link href="" rel="icon">
  <link href="{% static 'assets/img/apple-touch-icon.png'%}" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="{% static 'assets/vendor/bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/bootstrap-icons/bootstrap-icons.css' %}" rel="stylesheet">
  
  <link href="{% static 'assets/vendor/animate.css/animate.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/glightbox/css/glightbox.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}" rel="stylesheet">

 


  <!-- Add this in the <head> section of your base template or contact.html -->
    <script
    src="https://code.jquery.com/jquery-3.5.1.min.js"
    integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
    crossorigin="anonymous"></script>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
    integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
    crossorigin="anonymous"></script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" 
    integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" 
    crossorigin="anonymous"></script>

    <style>
        /* General Contact Section Styling */
        .partnership {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
            background-color: aliceblue;
        }

        .return-home {
            margin-top: 30px;
        }

        /* Flexbox layout for Contact Items */
        .partnership.partnership-items {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 30px;
        }

        
        .partnership-form {
            padding-top: 50px;
            padding-bottom: 80px;
            padding-left: 20px;
            padding-right: 20px;
            margin-top: 10px;
            margin-left: 25%;
            margin-right: 25%;
            background-color: aliceblue;
            box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
            border: solid 1px;
            border-color: gray;
            border-radius: 5px;
            
        }

        .partnership-form h2 {
            text-align: center;
            margin-bottom: 50px;
        }


        
        .partnership-form button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: skyblue;
            color: white;
            border: 1px;
            border-radius: 5px;
            cursor: pointer;
            transition: 0.4s;
            box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
        }

        .partnership-form button:hover {
            background: color-mix(in srgb, var(--accent-color), transparent 25%);
        }

        /* Mobile Responsiveness */
        @media (max-width: 1024px) {
            .partnership.partnership-items {
                flex-direction: column;
                align-items: center;
            }

            .partnership-message,
            .partnership-form {
                margin: unset;
            }

            .partnership-items span {
                margin-top: 40px;
            }

            /* Ensure the form and message areas stretch vertically if needed */
            .partnership-form {
                min-height: 100%;
            }
        }
    </style>
</head>
<body>

    <main>
        <!-- Partnership Submit -->
        <section id="partnership" class="partnership section">

            <div class="return-home">
                <p><a href="{% url 'homepage:index' %}">Return Home</a></p>
            </div>

            <div class="container partnership-items" data-aos="fade" data-aos-delay="100">
                <div >
                    {% if messages %}
                        <div id="success-message" class="alert alert-success" role="alert">
                            {% for message in messages %}
                                <p>{{ message }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <form action="" method="POST" class="partnership-form" data-aos="fade-up" data-aos-delay="200">
                        {%csrf_token%}

                        <h2>Partnership Form</h2>
                        <div class="row gy-4">

                            <!-- Name Input -->
                            <div class="col-md-6">
                                <input type="text" name="first_name" class="form-control" placeholder="First Name" required>


                            </div>

                            <div class="col-md-6">
                                <input type="text" name="last_name" class="form-control" placeholder="Last Name" required>
                            </div>

                            <!-- Email Input -->
                            <div class="col-md-6">
                                <input type="email" class="form-control" name="email" placeholder="Your Email" required>
                            </div>

                            <div class="col-md-6">
                                <input type="text" class="form-control" name="phone" placeholder="Phone Number" required="">
                            </div>

                            <!--Company-->
                            <div class="col-md-12">
                                <input type="text" class="form-control" name="company" placeholder="Your Company" required="">
                            </div>

                            <!-- Message Textarea -->
                            <div class="col-md-12">
                                <textarea class="form-control" name="message" rows="6" placeholder="Message" required></textarea>
                            </div>

                            {% for error in form.errors %}
                            <div class="alert text-danger">
                                {% if error %}
                                    <p>{{ error }}</p>
                                {% endif %}
                            </div>
                            {% endfor %}

                            <!-- Form Feedback and Submit -->
                            <div class="col-md-12 text-center">
                                <div class="partnership-message">{{sucess_message}}</div>
                                <button type="submit">Submit</button>
                            </div>
                        </div>
                    </form>
                    {%for p in profile %}
                    <span style="font-weight: 500;">{{p.title}} </span>
                    {%endfor%}


                    <script>
                        document.addEventListener("DOMContentLoaded", function() {
                            const successMessage = document.getElementById('success-message');
                            if (successMessage) {
                                setTimeout(function() {
                                    successMessage.style.display = 'none';
                                }, 5000);
                            }
                        });
                    </script>
                </div><!-- End Contact Form -->

            </div>
        </section><!-- Partnership -->
    </main>

    <!-- External JavaScript -->
    <script src="{% static 'assets/js/main.js' %}"></script>

</body>
</html>
