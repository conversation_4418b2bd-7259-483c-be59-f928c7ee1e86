{% extends "partials/base.html" %}
{% load static %}

{% block content %}
<main class="main">
  <section>

  </section>

    <!-- Project Section -->
    <section id="project" class=" section project">
        <div class="container project-items" data-aos="fade" data-aos-delay="100">
          <div class="project-message" data-aos="fade-up" data-aos-delay="200">
            <h1>{{project_plural_name}}</h1>
            {%for message in project_message %}
            <div>
              <h2>{{message.project_message}}</h2>
              
            </div>
            {%endfor%}
          </div>

          <div class="django-form">
            {% if messages %}
                <div id="success-message" class="alert alert-success" role="alert">
                    {% for message in messages %}
                        <p>{{ message }}</p>
                    {% endfor %}
                </div>
            {% endif %}
            <form id="project_form" method="POST" data-aos="fade-up" data-aos-delay="200">
              {% csrf_token %}
              <div class="row gy-4">      
                <!-- Name Input -->
                <div class="col-md-12">
                  <input type="text" name="name" class="form-control" placeholder="Full Name" required>
                </div>

                <!-- Email Input -->
                <div class="col-md-6">
                  <input type="email" class="form-control" name="email" placeholder="Your Email" required>
                </div>

                <!--Phone-->
                <div class="col-md-6">
                  <input type="text" class="form-control" name="phone" placeholder="Phone Number" required="">
                </div>

                <!--Company-->
                <div class="col-md-12">
                  <input type="text" class="form-control" name="subject" placeholder="Subject" required="">
                </div>

                <!-- Message Textarea -->
                <div class="col-md-12">
                  <textarea class="form-control" name="message" rows="6" placeholder="Message" required></textarea>
                </div>
  
                  <div class="col-md-12 text-center">
                      <button type="submit" class="btn-submit">Send Message</button>
                  </div>     
                </div>
            </form>

            <script>
              document.addEventListener("DOMContentLoaded", function() {
                  const successMessage = document.getElementById('success-message');
                  if (successMessage) {
                      setTimeout(function() {
                          successMessage.style.display = 'none';
                      }, 5000);
                  }
              });
          </script>
          </div>
        </div>
    </section>

  <!--Counter-->
  <section class="section egde">
    <div class="container">
      <div class="egde-items">
        <div class="edge-header " data-aos="fade-up">
          {%for count in counter %}
          <h1>{{count.title}} </h1>
          {%endfor%}
        </div>
        <div class="edge-header " data-aos="fade-up">
        </div>
        <div class="container" data-aos="fade-up" data-aos-delay="100">
          <ul class="edge-outcome">
            {%for feed in counter_feed%}
            <li>
              <h1 class="counter" data-target="{{feed.number}}">0%</h1>
              <p style="font-weight: 500;">{{feed.feed}} </p>
            </li>
            {%endfor%}
          </ul>
          
        </div>
      </div>
    </div>


    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // Select all counter elements
        const counters = document.querySelectorAll('.counter');  
        
        // Function to animate the numbers
        const animateCounter = function (counter) {
          const target = +counter.getAttribute('data-target');  // Get target value from data-target attribute
          let current = 0;  // Start from 0
          const increment = target / 100;  // Increment value
    
          function updateCounter() {
            if (current < target) {
              current += increment;  // Increase current value
              counter.innerText = Math.ceil(current) + '%';  // Update the text
              requestAnimationFrame(updateCounter);  // Continue animating
            } else {
              counter.innerText = target + '%';  // Ensure the final value is set
            }
          }
    
          updateCounter();  // Start the animation
        };
    
        // Intersection Observer options
        const options = {
          root: null,  // Use the viewport as the root
          rootMargin: '0px',
          threshold: 0.5  // Trigger the animation when 50% of the element is in view
        };
    
        // Intersection Observer callback function
        const observer = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              // If the counter element is in view, animate it
              animateCounter(entry.target);
              observer.unobserve(entry.target);  // Stop observing the counter after it has animated
            }
          });
        }, options);
    
        // Observe each counter element
        counters.forEach(counter => {
          observer.observe(counter);  // Start observing each counter element
        });
      });
    </script>
    
    
  </section>
    

  </main>
{% endblock %}
