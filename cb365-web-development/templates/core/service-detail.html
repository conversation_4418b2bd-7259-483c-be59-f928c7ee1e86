{% extends 'partials/base.html' %}
{% load static %}
{% block content %}

<main class="main">

  {% if service.breadcrumbs_image %}
  <style>
      .breadcrumbs-image {
          background-image: url('{{ service.breadcrumbs_image.url }}');
          background-size: cover;
          background-position: center;
          top: 0;
          left: 0;
          width: 100%;
          height: 350px; 
          z-index: -2; 
          overflow: hidden; 
          
      }
      .breadcrumbs-image::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.2);
          backdrop-filter: blur(0.1px);
          z-index: -1;
        }   
        @media (max-width:1024px){
          .breadcrumbs-image {
          
          height: 300px; 
          
          
          }
        }
  </style>    
  {%endif%}


  <!-- Page Title -->
  <div class="page-title breadcrumbs-image">
    <div class="container position-relative ">
      <h1 style="color: white;">{{ service.title }}</h1>
      <p style="color: white;">{{service.description}}</p>
    </div>
  </div>


  <!-- Service Details Section -->
  <section id="service-details" class="service-details section">
    <div class="container">
      <div class="row gy-5">


        <!-- Table of Contents Sidebar -->
        <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
          <div class="service-box">
            <h4>Table of Content</h4>
            <div class="services-list">
              {% for content in service_content %}
                {% with sanitized_title=content.title|slugify %}
                  <a href="#content-{{ sanitized_title }}">
                    <i class="bi bi-arrow-right"></i>
                    <span style="font-weight: 500;">{{ content.title }}</span>
                  </a>
                {% endwith %}
              {% endfor %}
            </div>
          </div>
        </div>


        <!-- Main Content Section -->
        <div class="col-lg-8 ps-lg-5" data-aos="fade-up" data-aos-delay="200">
          {% for content in service_content %}
            {% with sanitized_title=content.title|slugify %}
              <div id="content-{{ sanitized_title }}">
                <!-- Service Content Title -->
                <h3>{{ content.title }}</h3>
                <!-- Content Body -->
                <p>{{ content.body|safe }}</p>

                {% if content.service_images.all %}
                <div class="detail-gallery">
                    <!-- MAIN SLIDES -->
                    <div class="product-image-slider">
                        {% for image in content.service_images.all %}
                        <figure class="border-radius-10">
                            <img src="{{ image.image.url }}" alt="" data-zoom-image="{{ image.image.url }}" loading="lazy" />
                        </figure>
                        {% endfor %}
                    </div>
                    <!-- THUMBNAILS -->
                     {% if content.service_images.all|length > 1 %}
                    <div class="slider-nav-thumbnails">
                        {% for image in content.service_images.all %}
                        <div><img src="{{ image.image.url }}" width="100%" height="100" style="object-fit: cover;" alt="" /></div>
                        {% endfor %}
                    </div>
                    {%endif%}
                </div>
                {% endif %}

                {% if content.image %}
                <div class="detail-gallery">
                    <!-- SINGLE IMAGE -->
                    <div class="single-image">
                      <img src="{{ content.image.url }}" alt="" data-zoom-image="{{ content.image.url }}" loading="lazy" />
                    </div>
                </div>
                {% endif %}

              </div>
            {% endwith %}

            <!-- Extras Section -->
            <div class="container extras-items">
              {% if content.extras.all %}
              <ul class="extras">
                {% for extra in content.extras.all %}
                  <li>
                    <div>
                      <i class="bi bi-check2-circle"></i>
                      <span style="font-weight: 500; font-size: 20px;">{{ extra.title }}</span>
                    </div>
                    <p style="font-weight: 500;">{{ extra.text }}</p>
                  </li>
                {% endfor %}
              </ul>
              {%endif%}
            </div>

            
          {% endfor %}
        </div>
      </div>
    </div>
  </section>

</main>

{% endblock content %}
