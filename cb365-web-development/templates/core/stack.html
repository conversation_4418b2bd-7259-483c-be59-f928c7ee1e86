{% extends 'partials/base.html' %}
{% load static %}
{% block content %}
<main class="main">


    <!-- Page Title -->
    <div class="page-title" >
      <div class="container position-relative ">
        <h1> {{stack_plural_name}} </h1>
      </div>
    </div><!-- End Page Title -->

    <!-- Starter Section Section -->
    <section id="stack-page" class="stack-page section">
      {%for stk in stack %}
      <div class="container stack-page-items" data-aos="fade-up">

        <div class="stack-page-header">
          
          <h1>{{stk.title}} </h1>
          <img src="{{stk.image.url}}" alt="">
          
        </div>

        <div class="">
            
              <table>
                <tbody>
                    {% for s in stk.stack_list.all %}
                      <tr>
                        <td><h5>{{ s.title }}</h5></td>
                        <td><p>{{ s.body|safe }}</p></td>
                      </tr>
                    {% endfor %}
                </tbody>
              </table>
            
        </div>
      </div>
      {% endfor %}
    </section><!-- /Starter Section Section -->

</main>

{% endblock content %}