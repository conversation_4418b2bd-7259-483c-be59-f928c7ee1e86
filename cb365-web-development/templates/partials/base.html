{% load static %}

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cobber365</title>
  <meta name="description" content="">
  <meta name="keywords" content="">

  <!-- Favicons -->
   {%for p in profile %}
  <link href="{%if p.favicon%}{{p.favicon.url}}{%endif%}" rel="icon" style="height: 0.1px; width: 100%;">
  <link href="{% static 'assets/img/apple-touch-icon.png'%}" rel="apple-touch-icon">
  {%endfor%}

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Asap+Condensed:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Bebas+Neue&family=Exo+2:ital,wght@0,100..900;1,100..900&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Lora:ital,wght@0,400..700;1,400..700&family=Merriweather+Sans:ital,wght@0,300..800;1,300..800&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Quicksand:wght@300..700&family=Roboto+Slab:wght@100..900&display=swap" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Audiowide&family=Exo+2:ital,wght@0,100..900;1,100..900&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Orbitron:wght@400..900&family=PT+Sans:ital,wght@0,400;0,700;1,400;1,700&family=Source+Sans+3:ital,wght@0,200..900;1,200..900&family=Titillium+Web:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap" rel="stylesheet">



  <!-- Vendor CSS Files -->
  <link href="{% static 'assets/vendor/bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/bootstrap-icons/bootstrap-icons.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/aos/aos.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/animate.css/animate.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/glightbox/css/glightbox.min.css' %}" rel="stylesheet">
  <link href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="{% static 'assets/css/main.css' %}" rel="stylesheet">
  <link href="{% static 'assets/css/about-us.css' %}" rel="stylesheet">
  <link href="{% static 'assets/css/team.css' %}" rel="stylesheet">
  <link href="{% static 'assets/css/stack.css' %}" rel="stylesheet">
  <link href="{% static 'assets/css/careers.css' %}" rel="stylesheet">
  <link href="{% static 'assets/css/service.css' %}" rel="stylesheet"/>

  <!-- Add this in the <head> section of your base template or contact.html -->
  <script
  src="https://code.jquery.com/jquery-3.5.1.min.js"
  integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
  crossorigin="anonymous"></script>

  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
  integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
  crossorigin="anonymous"></script>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" 
  integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" 
  crossorigin="anonymous"></script>

  <!-- Slick.js CSS -->
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"/>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/elevatezoom/3.0.8/jquery.elevatezoom.min.js"></script>

  <!-- Slick.js JS -->
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>



</head>

<body class="index-page">

  <header id="header" class="header d-flex align-items-center fixed-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center justify-content-between">

      <a href="{%url 'homepage:index' %}" class="logo d-flex align-items-center">
         {% for p in profile %}
         {%if p.logo %}
        <img src="{{p.logo.url}}" alt="Cobber365" style="height: auto; max-width: 150px;">
        {%endif%}
        {%endfor%}
      </a>

      <nav id="navmenu" class="navmenu">
        
        <ul>
          <li><a href="{% url 'homepage:index' %}" class="">Home</a></li>
          <li class="dropdown"><a href="#"> <span>{{services_plural_name}}</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
          <ul class="drop-menu">
            {%for svc in services %}
            <li> <a href="{% url 'services_and_more:service' svc.slug %}">{{svc.title}}</a> </li>
            {% endfor %}
          </ul>
          </li>
            <li class="dropdown"><a href="#"> <span>{{about_us_plural_name}} </span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
              <ul>
                {% for about in about_us %}
                <li><a href="{% url 'cobber365:about-us' about.slug %}">{{about.title}} </a></li>
                {%endfor%}      
                {%for team in our_team %}
                <li><a href="{% url 'cobber365:our-team' team.slug %}">{{team_plural_name}} </a></li>
                {%endfor%}
                <li><a href="{% url 'services_and_more:stack' %}">{{stack_plural_name}} </a></li>               
              </ul>
              </li>
          <li><a href="{% url 'cobber365:careers' %}">{{careers_plural_name}} </a></li>
          <li><a href="{% url 'cobber365:contact' %}">{{contact_plural_name}} </a></li>
          <div class="project-base">
            <li><a href="{% url 'mail_and_forms:project' %}">{{project_plural_name}} </a></li>
          </div>
          
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

 {% block content %}

 {% endblock content %}

  <footer id="footer" class="footer dark-background">

    {%for p in profile %}
    <div class="container">
      <div class="footer-items">

        <div class="footer-items-container">
          <h4>Follow Us</h4>
         <!-- Social handle -->
        <div class="social-links d-flex ">
          {%if p.x%}
          <a href="{{p.x}}" target="_blank"><i class="bi bi-twitter-x"></i></a>
          {%endif%}

          {%if p.facebook%}
          <a href="{{p.facebook}}" target="_blank"><i class="bi bi-facebook"></i></a>
          {%endif%}

          {%if p.instagram %}
          <a href="{{p.instagram}}" target="_blank"><i class="bi bi-instagram"></i></a>
          {%endif%}

          {%if p.linkedin%}
          <a href="{{p.linkedin}}" target="_blank"><i class="bi bi-linkedin"></i></a>
          {%endif%}
        </div>
        
        </div>


        <!-- Contact -->
        <div class="footer-items-container">
        <h4>Services</h4>
        <div class="services-footer">
            {%for service in services%}
            <p><a href="{%url 'services_and_more:service' service.slug %}">{{service.title}} </a></p>
            
            {%endfor%}
        </div>
        
        
        
        </div>
        

        <div class="footer-items-container">
          <h4>Quick Links</h4>
          <!-- Quick links -->
          <div class="footer-quicklinks">
            {% for pages in pages_base %}
            <p><a href="{%url 'src:pages' pages_slug=pages.slug %}">{{pages.title}}</a></p>
            {% endfor %}
            <p><a href="{% url 'cobber365:careers' %}">Careers</a></p>
            <p><a href="{% url 'mail_and_forms:partnership' %}" target="_blank">Partner with Us?</a></p>
          </div>
        </div>
        


       <!-- Newsletter -->
      <div class="newsletter-section footer-items-container">
        <h4>Newsletter</h4>
        <div>
          <p>Subscribe to  get the lastest tech updates.</p>
        </div>
        <div>
          <form id="newsletter-form" method="POST" action="{% url 'mail_and_forms:newsletter' %}">
            {% csrf_token %}
            <input class="form-control" type="email" name="subscriber" id="subscriber" placeholder="Enter your email" />
            <button type="submit"><i class="bi bi-chevron-right"></i></button>
        </form>
        
        <div id="result-message"></div>

         <!-- Loading Spinner (initially hidden) -->
         <div id="loading-spinner" style="display: none;">
          <i class="bi bi-arrow-repeat" style="font-size: 24px; color: white;"></i>
        </div>


        <script>
          document.addEventListener('DOMContentLoaded', function() {
              const form = document.getElementById('newsletter-form');
              const resultMessage = document.getElementById('result-message');
              const loadingSpinner = document.getElementById('loading-spinner');
              
              form.addEventListener('submit', function(event) {
                  event.preventDefault();  // Prevent the default form submission
  
                  // Show the loading spinner
                  loadingSpinner.style.display = 'block';
  
                  // Get form data
                  const formData = new FormData(form);
                  
                  // Send AJAX request
                  fetch("{% url 'mail_and_forms:newsletter' %}", { 
                      method: "POST",
                      body: formData,
                  })
                  .then(response => response.json())
                  .then(data => {
                      // Hide the loading spinner
                      loadingSpinner.style.display = 'none';
  
                      if (data.message) {
                          // Success: Show success message
                          resultMessage.textContent = data.message;
                          resultMessage.style.color = "green";
                          
                          // Clear the form after successful subscription
                          form.reset();
                      } else if (data.error) {
                          // Error: Show error message
                          resultMessage.textContent = data.error;
                          resultMessage.style.color = "red";
                      } else if (data.errors) {
                          // If form has validation errors
                          let errorMessages = Object.values(data.errors).join(', ');
                          resultMessage.textContent = `Errors: ${errorMessages}`;
                          resultMessage.style.color = "red";
                      }
                  })
                  .catch(error => {
                      // Hide the loading spinner in case of an error
                      loadingSpinner.style.display = 'none';
                      
                      resultMessage.textContent = 'There was an error. Please try again later.';
                      resultMessage.style.color = "red";
                  });
              });
          });
        </script>
        
        </div>
      </div>
    </div>
    <div class="container">
      <div class="copyright">
        <span>Copyright</span> <strong class="px-1 sitename">{{p.title}} </strong> <span>All Rights Reserved</span>
      </div>
      <div class="credits">
      </div>
    </div>
    {%endfor%}
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Preloader -->
  <div id="preloader"></div>

  <!-- Vendor JS Files -->
  <script src="{% static 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
  <script src="{% static 'assets/vendor/aos/aos.js' %}"></script>
  <script src="{% static 'assets/vendor/glightbox/js/glightbox.min.js' %}"></script>
  <script src="{% static 'assets/vendor/imagesloaded/imagesloaded.pkgd.min.js' %}"></script>
  <script src="{% static 'assets/vendor/isotope-layout/isotope.pkgd.min.js' %}"></script>
  <script src="{% static 'assets/vendor/swiper/swiper-bundle.min.js' %}"></script>


  <!-- Main JS File -->
  <script src="{% static 'assets/js/main.js' %}"></script>
  <script src="{% static 'assets/js/service.js' %}"></script>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script

  <!-- Include the full version of jQuery -->

</body>

</html>