

# Target Tracking Scaling Policy (CPU-based scaling)
resource "aws_autoscaling_policy" "cpu_policy" {
  name                    = "webapp-cpu-utilization"
  autoscaling_group_name = aws_autoscaling_group.app_asg.name
  policy_type             = "TargetTrackingScaling"

  target_tracking_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ASGAverageCPUUtilization"
    }
    target_value      = 80.0
    disable_scale_in  = false
  }

  estimated_instance_warmup = 300
}



# CloudWatch Alarm for alerting (monitoring only, not triggering scaling)
resource "aws_cloudwatch_metric_alarm" "high_cpu_alert" {
  alarm_name          = "webapp-cpu-utilization-alert"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 3
  datapoints_to_alarm = 3
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 300
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Alert when average CPU > 80% for 15 minutes across instances in ASG"
  treat_missing_data  = "ignore"

  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_asg.name
  }

  tags = merge(local.common_tags, {
    Name    = "webapp-cpu-utilization-alert"
    Purpose = "Monitoring"
  })
}



# need to resolve why its insufficient
resource "aws_cloudwatch_metric_alarm" "app_memory_alarm" {
  alarm_name          = "app-memory-utilization"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  datapoints_to_alarm = "3"
  metric_name         = "mem_used_percent"
  namespace           = "CWAgent"
  period              = "300"
  statistic           = "Maximum"
  threshold           = "80"  # Higher threshold than bastions
  alarm_description   = "Triggers when memory usage exceeds 80% in App ASG"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "notBreaching" # Recommended for production

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_asg.name
  }

  tags = merge(local.common_tags, {
    Name    = "app-memory-utilization"
    Purpose = "Monitoring"
  })
}






# Disk Usage Alarm (Monitoring Only)
resource "aws_cloudwatch_metric_alarm" "app_disk_alarm" {
  alarm_name          = "webapp-disk-utilization"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "disk_used_percent"
  namespace          = "CWAgent"
  period             = "300"
  statistic          = "Maximum"
  threshold          = "85"
  alarm_description  = "Disk monitoring (does not trigger scaling) - 15 minute threshold"
  alarm_actions      = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions         = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data = "notBreaching"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_asg.name
    path                = "/"
    fstype              = "ext4"
    device              = "xvda1"
  }

  tags = merge(local.common_tags, {
    Name    = "webapp-disk-utilization"
    Purpose = "Monitoring"
  })
}






