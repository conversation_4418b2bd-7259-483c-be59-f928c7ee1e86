


# CloudWatch Alarm for CPU Utilization (Notification Only)
resource "aws_cloudwatch_metric_alarm" "cpu_alarm" {
  alarm_name          = "bastion-cpu-utilization"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Sum"
  threshold           = "80"
  alarm_description   = "Alarm when CPU exceeds 80% for 15 minutes"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions         = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "ignore"  # Ignore when instances are stopped

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
  }

  tags = merge(local.common_tags, {
    Name    = "bastion-cpu-utilization"
    Purpose = "Monitoring"
  })
}




# Memory Utilization Alarm (Monitoring Only)
resource "aws_cloudwatch_metric_alarm" "memory_alarm" {
  alarm_name          = "bastion-memory-utilization"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  datapoints_to_alarm = "3"
  metric_name         = "mem_used_percent"
  namespace           = "CWAgent"  # Must match SSM config
  period              = "300"
  statistic           = "Maximum"
  threshold           = "80"
  alarm_description   = "Triggers when memory > 80% for 15 minutes"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "notBreaching"  # Better for production

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
    
  }

  tags = merge(local.common_tags, {
    Name    = "bastion-memory-utilization",
    Purpose = "Monitoring"
  })
}




# Disk Usage Alarm (Monitoring Only)
# Disk Usage Alarm (Monitoring Only)
# Disk Usage Alarm (Monitoring Only)
resource "aws_cloudwatch_metric_alarm" "disk_alarm" {
  alarm_name          = "bastion-disk-utilization"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "disk_used_percent"
  namespace           = "CWAgent"
  period              = "300"
  statistic          = "Average"
  threshold          = "80"
  alarm_description   = "Disk monitoring (does not trigger scaling) - 15 minute threshold"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions         = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data = "notBreaching"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
    path                 = "/"
    fstype              = "ext4"
    device              = "xvda1"
  }

  tags = merge(local.common_tags, {
    Name    = "bastion-disk-utilization"
    Purpose = "Monitoring"
  })
}




# Storage Alarm for EFS 
resource "aws_cloudwatch_metric_alarm" "efs_usage_percent_alarm" {
  alarm_name          = "efs-usage-percent-alarm"
  alarm_description   = "Alarm when EFS usage exceeds 80% of 100 GiB"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 3
  threshold           = 80  # Percent
  treat_missing_data  = "ignore"
  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]

  metric_query {
    id = "m1"
    metric {
      metric_name = "StorageBytes"
      namespace   = "AWS/EFS"
      period      = 300
      stat        = "Average"
      dimensions = {
        FileSystemId = aws_efs_file_system.shared.id
        StorageClass = "Total"
      }
    }
  }

  metric_query {
    id          = "e1"
    expression  = "m1 / 107374182400 * 100"  # 100 GiB in bytes
    label       = "EFS Usage Percent"
    return_data = true
  }

  tags = merge(local.common_tags, {
    Name    = "efs-usage-percent-alarm"
    Purpose = "Detect high EFS storage usage"
  })
}

resource "aws_cloudwatch_metric_alarm" "efs_ia_access_alarm" {
  alarm_name          = "efs-infrequent-access-usage"
  alarm_description   = "Alarm when EFS IA is accessed (MeteredIOBytes > 0)"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  threshold           = 0
  period              = 300
  statistic           = "Sum"
  namespace           = "AWS/EFS"
  metric_name         = "MeteredIOBytes"
  treat_missing_data  = "ignore"
  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]

  dimensions = {
    FileSystemId = aws_efs_file_system.shared.id
    StorageClass = "Standard"
  }

  tags = merge(local.common_tags, {
    Name    = "efs-ia-access-alarm"
    Purpose = "Detect EFS IA request charges"
  })
}

