# CloudWatch Dashboard for CPU Utilization Monitoring
resource "aws_cloudwatch_dashboard" "bastion_performance_dashboard" {
  dashboard_name = "Bastion-Performance-Dashboard"
  dashboard_body = jsonencode({
    widgets = [
      # CPU Utilization Widget
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/EC2", "CPUUtilization", "AutoScalingGroupName", aws_autoscaling_group.bastion_asg.name, { "stat": "Average", "label": "Average CPU %" }],
            ["...", { "stat": "Maximum", "label": "Max CPU %" }],
            ["...", { "stat": "Minimum", "label": "Min CPU %" }]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "Bastion CPU Utilization"
          period  = 300
          annotations = {
            horizontal = [
              {
                color = "#ff0000"
                label = "Alarm Threshold (80%)"
                value = 80
              }
            ]
          }
          }
      },
      
      # Alarm Status Widget
      {
        type   = "alarm"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          title  = "Alarm Status"
          alarms = [aws_cloudwatch_metric_alarm.cpu_alarm.arn]
        }
      },
      
      # Instance Count Widget
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/AutoScaling", "GroupInServiceInstances", "AutoScalingGroupName", aws_autoscaling_group.bastion_asg.name]
          ]
          view    = "singleValue"
          region  = var.aws_region
          title   = "Bastion Instance Count"
          period  = 300
        }
      },
      
      # Network Performance Widget
      {
        type   = "metric"
        x      = 12
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/EC2", "NetworkIn", "AutoScalingGroupName", aws_autoscaling_group.bastion_asg.name],
            ["...", "NetworkOut"]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "Network Performance (Bytes)"
          period  = 300
        }
      }
    ]
  })
}

# CloudWatch Dashboard for Memory Monitoring
resource "aws_cloudwatch_dashboard" "bastion_memory_dashboard" {
  dashboard_name = "Bastion-Memory-Dashboard"
  dashboard_body = jsonencode({
    widgets = [
      # 1. Memory Utilization Graph (Primary Metric)
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["CWAgent", "mem_used_percent", "AutoScalingGroupName", aws_autoscaling_group.bastion_asg.name, { "stat": "Maximum", "label": "Max Memory %" }],
            ["...", { "stat": "Average", "label": "Avg Memory %" }],
            ["...", { "stat": "Minimum", "label": "Min Memory %" }]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "Bastion Memory Utilization (%)"
          period  = 300
          annotations = {
            horizontal = [
              {
                color = "#ff0000"
                label = "Alarm Threshold (80%)"
                value = 80
              }
            ]
          }
        }
      },

      # 2. Memory Alarm Status
      {
        type   = "alarm"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          title  = "Memory Alarm Status"
          alarms = [aws_cloudwatch_metric_alarm.memory_alarm.arn]
        }
      },

      # 3. Swap Usage (Critical for Memory Pressure)
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["CWAgent", "swap_used_percent", "AutoScalingGroupName", aws_autoscaling_group.bastion_asg.name, { "label": "Swap Used %" }]
          ]
          view    = "timeSeries"
          region  = var.aws_region
          title   = "Swap Usage (%)"
          period  = 300
        }
      },

      # 4. System Metrics (CPU + Disk for Context)
      {
        type   = "metric"
        x      = 12
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/EC2", "CPUUtilization", "AutoScalingGroupName", aws_autoscaling_group.bastion_asg.name, { "label": "CPU %" }],
            ["CWAgent", "disk_used_percent", "AutoScalingGroupName", aws_autoscaling_group.bastion_asg.name, { "label": "Disk Used %" }]
          ]
          view    = "timeSeries"
          stacked = false
          region  = var.aws_region
          title   = "CPU & Disk Utilization"
          period  = 300
        }
      }
    ]
  })
}