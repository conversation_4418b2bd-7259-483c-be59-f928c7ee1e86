
# Auto Scaling Group for Bastion Host
resource "aws_autoscaling_group" "bastion_asg" {
  name_prefix         = "bastion-asg-"
  desired_capacity    = 1
  max_size            = 1
  min_size            = 1
  vpc_zone_identifier = [aws_subnet.bastion_host_subnet.id]

  launch_template {
    id      = aws_launch_template.bastion_template.id
    version = "$Latest"
  }

  health_check_type         = "EC2"
  health_check_grace_period = 300

  force_delete = true

  # Updated with only valid metrics
  enabled_metrics = [
    "GroupMinSize",
    "GroupMaxSize",
    "GroupDesiredCapacity",
    "GroupInServiceInstances",
    "GroupInServiceCapacity",
    "GroupPendingInstances",
    "GroupPendingCapacity",
    "GroupTerminatingInstances",
    "GroupTerminatingCapacity",
    "GroupStandbyInstances",
    "GroupStandbyCapacity",
    "GroupTotalInstances",
    "GroupTotalCapacity",
    "WarmPoolMinSize",
    "WarmPoolDesiredCapacity",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolWarmedCapacity",
    "WarmPoolTotalCapacity",
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity"
  ]

  metrics_granularity = "1Minute"

  dynamic "tag" {
    for_each = merge(local.common_tags, {
      Name = "cb365-web-bastion-instance"
    })
    content {
      key                 = tag.key
      value               = tag.value
      propagate_at_launch = true
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [desired_capacity, target_group_arns]
  }

  instance_refresh {
    strategy = "Rolling"
    preferences {
      min_healthy_percentage = 0
      instance_warmup        = 300
    }
  }
}



resource "aws_launch_template" "bastion_template" {
  name_prefix = "bastion-template-"
  image_id    = var.cb365_bastion_host_ami
  #image_id      = data.aws_ami.ami.id
  instance_type = var.bastion_host_instance_type
  key_name      = var.bastion_host_key


  user_data = base64encode(<<-EOF
#!/bin/bash
set -e

# Main log file setup
LOG_FILE="/var/log/user-data.log"
exec > >(tee -a "$LOG_FILE") 2>&1

# Helper function for logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "===== STARTING USER DATA SCRIPT ====="

# Get region with better error handling
log "Getting instance metadata..."
TOKEN=$(curl -s -f -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600") || { log "ERROR: Failed to get IMDSv2 token"; exit 1; }
REGION=$(curl -s -f -H "X-aws-ec2-metadata-token: $TOKEN" "http://***************/latest/meta-data/placement/region") || { log "ERROR: Failed to get region"; exit 1; }
log "SUCCESS: Got region: $REGION"

# Get instance ID
INSTANCE_ID=$(curl -s -f -H "X-aws-ec2-metadata-token: $TOKEN" "http://***************/latest/meta-data/instance-id") || { log "ERROR: Failed to get instance ID"; exit 1; }
log "SUCCESS: Got instance ID: $INSTANCE_ID"

# Wait for network to be fully available
log "===== WAITING FOR NETWORK TO BE FULLY AVAILABLE ====="
MAX_WAIT=300  # 5 minutes maximum wait time
START_TIME=$(date +%s)

# Wait for DNS resolution to work properly
while [ $(($(date +%s) - START_TIME)) -lt $MAX_WAIT ]; do
    if host -t A debian.org &>/dev/null || host -t A google.com &>/dev/null; then
        log "SUCCESS: DNS resolution is working"
        break
    fi
    log "Waiting for DNS resolution... ($(($MAX_WAIT - $(date +%s) + $START_TIME)) seconds remaining)"
    sleep 10
done

# Wait for actual connectivity
for i in {1..30}; do
    if ping -c 1 -W 5 ******* &>/dev/null || ping -c 1 -W 5 ******* &>/dev/null; then
        log "SUCCESS: Network connectivity confirmed"
        break
    fi
    log "Waiting for network connectivity... attempt $i of 30"
    sleep 10
    
    # After 10 attempts, try to restart networking
    if [ $i -eq 10 ]; then
        log "Attempting to restart networking services"
        sudo systemctl restart networking.service &>/dev/null || true
        sudo systemctl restart NetworkManager.service &>/dev/null || true
    fi
done

# Install packages
log "===== INSTALLING PACKAGES ====="
log "Detected Debian system"

# Try apt update with retries and longer timeout
MAX_RETRIES=10
for i in $(seq 1 $MAX_RETRIES); do
    log "Attempting apt update (try $i of $MAX_RETRIES)"
    if sudo apt-get clean && sudo apt-get update -o Acquire::https::Timeout=120 -o Acquire::http::Timeout=120 -o Acquire::Retries=10; then
        log "SUCCESS: apt update completed"
        break
    else
        log "WARNING: apt update failed on attempt $i"
        if [ $i -eq $MAX_RETRIES ]; then
            log "WARNING: Continuing without apt update after $MAX_RETRIES attempts"
        else
            # Try to fix package lists
            sudo rm -rf /var/lib/apt/lists/* || true
            sleep 30  # Longer sleep between retries
        fi
    fi
done

# Install required packages
log "Installing required packages..."
sudo DEBIAN_FRONTEND=noninteractive apt-get install -y nginx jq unzip rpcbind cron || log "WARNING: Failed to install some packages"

# Try to install nfs-common with multiple retries
MAX_RETRIES=5
for i in $(seq 1 $MAX_RETRIES); do
    log "Attempting to install nfs-common (try $i of $MAX_RETRIES)"
    if sudo DEBIAN_FRONTEND=noninteractive apt-get install -y nfs-common; then
        log "SUCCESS: Installed nfs-common"
        break
    else
        log "WARNING: Failed to install nfs-common on attempt $i"
        sleep 10
    fi
done

# Start and enable rpcbind
log "Starting and enabling rpcbind..."
sudo systemctl start rpcbind || log "WARNING: Failed to start rpcbind"
sudo systemctl enable rpcbind || log "WARNING: Failed to enable rpcbind"
sudo systemctl start nfs-common || log "WARNING: Failed to start nfs-common"

# Load NFS kernel modules
log "Loading NFS kernel modules..."
sudo modprobe nfs || log "WARNING: Failed to load nfs module"
sudo modprobe nfsv4 || log "WARNING: Failed to load nfsv4 module"
sleep 10  # Give NFS services time to initialize

# Install AWS CLI
log "Installing AWS CLI..."
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS CLI
log "Configuring AWS CLI..."
mkdir -p ~/.aws
cat > ~/.aws/config << EOF_AWS_CONFIG
[default]
region = $REGION
output = json
EOF_AWS_CONFIG

# Verify AWS credentials
log "Verifying AWS credentials..."
aws sts get-caller-identity --region "$REGION" --endpoint-url "https://sts.$REGION.amazonaws.com" || {
    log "ERROR: No valid AWS credentials found. Ensure the instance has an IAM role with appropriate permissions."
}

# Associate EIP with retry logic
log "Associating Elastic IP..."
for i in {1..5}; do
    if sudo aws ec2 associate-address \
        --instance-id $INSTANCE_ID \
        --allocation-id ${aws_eip.bastion_host_eip.id} \
        --allow-reassociation \
        --region $REGION; then
        log "EIP associated successfully"
        break
    fi
    log "EIP association attempt $i failed, retrying in 10 seconds..."
    sleep 10
done

# Set up EFS mount
log "===== SETTING UP EFS MOUNT ====="
MOUNT_POINT="/var/www/html/nginx"
EFS_ID="${aws_efs_file_system.shared.id}"
EFS_DNS="$${EFS_ID}.efs.$${REGION}.amazonaws.com"
log "EFS DNS: $${EFS_DNS}"

# Create mount directory
log "Creating mount directories..."
sudo mkdir -p "$MOUNT_POINT" || { log "ERROR: Failed to create mount directory"; exit 1; }
log "SUCCESS: Created mount directories"

# Add EFS DNS to hosts file with multiple IP resolution attempts
log "Ensuring EFS DNS resolution..."
EFS_RESOLVED=false

# Try to get mount target IPs using AWS CLI
if command -v aws &> /dev/null; then
    log "Attempting to get EFS mount targets using AWS CLI..."
    MOUNT_TARGETS=$(sudo aws efs describe-mount-targets --file-system-id "$EFS_ID" --region "$REGION" 2>/dev/null)
    if [ $? -eq 0 ]; then
        MOUNT_TARGET_IP=$(echo "$MOUNT_TARGETS" | grep -o '"IpAddress": "[^"]*"' | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
        if [ -n "$MOUNT_TARGET_IP" ]; then
            log "Found EFS mount target IP: $MOUNT_TARGET_IP"
            echo "$MOUNT_TARGET_IP $${EFS_DNS}" | sudo tee -a /etc/hosts > /dev/null
            log "Added EFS mount target IP to hosts file"
            EFS_RESOLVED=true
        fi
    else
        log "WARNING: Failed to get EFS mount targets using AWS CLI"
    fi
fi

# Try standard DNS resolution methods if AWS CLI method failed
if [ "$EFS_RESOLVED" = false ]; then
    if command -v dig &> /dev/null; then
        log "Attempting to resolve EFS DNS using dig..."
        EFS_IP=$(dig +short "$${EFS_DNS}")
        if [ -n "$EFS_IP" ]; then
            log "Resolved EFS DNS using dig: $EFS_IP"
            echo "$EFS_IP $${EFS_DNS}" | sudo tee -a /etc/hosts > /dev/null
            EFS_RESOLVED=true
        fi
    fi
    
    if [ "$EFS_RESOLVED" = false ] && command -v nslookup &> /dev/null; then
        log "Attempting to resolve EFS DNS using nslookup..."
        EFS_IP=$(nslookup "$${EFS_DNS}" | grep -A1 'Name:' | grep 'Address:' | awk '{print $2}')
        if [ -n "$EFS_IP" ]; then
            log "Resolved EFS DNS using nslookup: $EFS_IP"
            echo "$EFS_IP $${EFS_DNS}" | sudo tee -a /etc/hosts > /dev/null
            EFS_RESOLVED=true
        fi
    fi
    
    # Try AWS internal DNS server as last resort
    if [ "$EFS_RESOLVED" = false ] && command -v dig &> /dev/null; then
        log "Attempting to query AWS DNS server directly..."
        EFS_IP=$(dig @*************** +short "$${EFS_DNS}")
        if [ -n "$EFS_IP" ]; then
            log "Resolved EFS DNS using AWS DNS server: $EFS_IP"
            echo "$EFS_IP $${EFS_DNS}" | sudo tee -a /etc/hosts > /dev/null
            EFS_RESOLVED=true
        fi
    fi
fi

# Mount with retries
log "Attempting to mount EFS..."
mount_success=false
MAX_MOUNT_RETRIES=5

for attempt in $(seq 1 $MAX_MOUNT_RETRIES); do
    log "Mount attempt $attempt of $MAX_MOUNT_RETRIES"
    
    # Use the variables to construct the mount command
    if sudo mount -t nfs4 -o nfsvers=4.1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport "$${EFS_DNS}:/" "$MOUNT_POINT" 2>/tmp/mount_error.log; then
        log "SUCCESS: Mounted EFS with NFS4"
        mount_success=true
        break
    else
        log "Mount error: $(cat /tmp/mount_error.log)"
        
        # On the third attempt, restart services
        if [ $attempt -eq 3 ]; then
            log "Restarting NFS and RPC services"
            sudo systemctl restart rpcbind || log "WARNING: Failed to restart rpcbind"
            sudo systemctl restart nfs-common || log "WARNING: Failed to restart nfs-common"
            sleep 20  # Longer wait after service restart
        fi
        
        sleep 15  # Longer wait between retries
    fi
done

if [ "$mount_success" = false ]; then
    log "WARNING: Failed to mount EFS after $MAX_MOUNT_RETRIES attempts"
    log "Creating local fallback directory..."
    sudo mkdir -p "$MOUNT_POINT/html" || { log "ERROR: Failed to create fallback directory"; exit 1; }
    echo "<html><body><h1>EFS Mount Failed</h1><p>The system was unable to mount the EFS filesystem after multiple attempts.</p></body></html>" | sudo tee "$MOUNT_POINT/html/index.html" > /dev/null
    log "Created fallback directory and index.html"
else
    log "EFS mounted successfully"
fi

# Set permissions
log "Setting permissions..."
sudo chmod -R 755 "$MOUNT_POINT" || { log "WARNING: Failed to set permissions on $MOUNT_POINT"; }
sudo chmod -R +r "$MOUNT_POINT" || { log "WARNING: Failed to set read permissions on $MOUNT_POINT"; }
log "Permissions set"

# Add to fstab if mount successful
if mount | grep -q "$MOUNT_POINT"; then
    log "Adding EFS mount to fstab..."
    if ! grep -q "$${EFS_DNS}" /etc/fstab; then
        echo "$${EFS_DNS}:/ $MOUNT_POINT nfs4 nfsvers=4.1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport,_netdev 0 0" | sudo tee -a /etc/fstab > /dev/null
        log "SUCCESS: Added EFS mount to fstab"
    else
        log "INFO: EFS mount already in fstab"
    fi
fi

# Set up Nginx load balancer
log "===== SETTING UP NGINX LOAD BALANCER ====="

# Create necessary directories and files
#sudo mkdir -p /etc/nginx/conf.d
#sudo mkdir -p /var/log/nginx
NGINX_LOG_FILE="/var/log/nginx-asg-update.log"
touch $NGINX_LOG_FILE
chmod 644 $NGINX_LOG_FILE

# Set ASG name
ASG_NAME="cb365-webapp-asg"
log "Using ASG name: $ASG_NAME"

# Create the update script
cat > /usr/local/bin/update-nginx-config << 'EOFSCRIPT'
#!/bin/bash
set -e

# Log file setup
LOG_FILE="/var/log/nginx-asg-update.log"

# Helper function for logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Get region from instance metadata
TOKEN=$(curl -s -f -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
REGION=$(curl -s -f -H "X-aws-ec2-metadata-token: $TOKEN" "http://***************/latest/meta-data/placement/region")
ASG_NAME="cb365-webapp-asg"

log "Starting Nginx configuration update"
log "Region: $REGION"
log "ASG Name: $ASG_NAME"

# Get instance IDs in the ASG
INSTANCE_IDS=$(aws autoscaling describe-auto-scaling-groups \
    --auto-scaling-group-name "$ASG_NAME" \
    --region "$REGION" \
    --query "AutoScalingGroups[0].Instances[?LifecycleState=='InService'].InstanceId" \
    --output text)

if [ -z "$INSTANCE_IDS" ]; then
    log "No instances found in ASG"
    exit 1
fi

log "Found instances: $INSTANCE_IDS"

# Create upstream configuration
UPSTREAM_CONF="/etc/nginx/conf.d/upstreams.conf"
echo "upstream backend {" > "$UPSTREAM_CONF"

# For each instance ID, get its private IP and add to config
for INSTANCE_ID in $INSTANCE_IDS; do
    PRIVATE_IP=$(aws ec2 describe-instances \
        --instance-ids "$INSTANCE_ID" \
        --region "$REGION" \
        --query "Reservations[0].Instances[0].PrivateIpAddress" \
        --output text)
    
    if [ "$PRIVATE_IP" != "None" ] && [ -n "$PRIVATE_IP" ]; then
        echo "    server $PRIVATE_IP:8000;" >> "$UPSTREAM_CONF"
        log "Added server: $PRIVATE_IP"
    fi
done

echo "}" >> "$UPSTREAM_CONF"

# Test and reload Nginx
if nginx -t; then
    systemctl reload nginx
    log "Nginx configuration updated and reloaded successfully"
else
    log "ERROR: Invalid Nginx configuration"
    exit 1
fi
EOFSCRIPT

# Make the script executable
chmod +x /usr/local/bin/update-nginx-config


# Set up cron job
log "Setting up cron job for Nginx configuration updates"
cat > /etc/cron.d/nginx-asg-update << 'EOFCRON'
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/aws/bin
*/5 * * * * root /usr/local/bin/update-nginx-config
EOFCRON

# Set proper permissions
chmod 644 /etc/cron.d/nginx-asg-update

# Initial Nginx configuration update
log "Performing initial Nginx configuration update"
/usr/local/bin/update-nginx-config

# Ensure cron is running
systemctl enable cron
systemctl restart cron

log "===== USER DATA SCRIPT COMPLETED SUCCESSFULLY ====="
EOF
  )


  iam_instance_profile {
    name = aws_iam_instance_profile.instance_profile.name
  }

  monitoring {
    enabled = true
  }

  network_interfaces {
    associate_public_ip_address = true
    subnet_id                   = aws_subnet.bastion_host_subnet.id
    security_groups             = [aws_security_group.bastion_sg.id]
    delete_on_termination       = true
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 2
  }

  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      encrypted   = true
      volume_size = 8
      volume_type = "gp3"
    }
  }

  depends_on = [
    aws_autoscaling_group.app_asg,
    aws_efs_mount_target.mount_target
  ]


  tags = merge(local.common_tags, {
    Name = "cb365-web-bastion-instance"
  })

  lifecycle {
    create_before_destroy = true
  }
}
