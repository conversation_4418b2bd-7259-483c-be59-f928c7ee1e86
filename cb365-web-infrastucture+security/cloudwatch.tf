/*

# CloudWatch Alarm for CPU Utilization (Notification Only)
resource "aws_cloudwatch_metric_alarm" "cpu_alarm" {
  alarm_name          = "bastion-cpu-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "Alarm when CPU exceeds 80% for 15 minutes"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions         = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "ignore"  # Ignore when instances are stopped

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
  }

  tags = merge(local.common_tags, {
    Name    = "bastion-cpu-utilization"
    Purpose = "Monitoring"
  })
}


# CloudWatch Alarm for webapp CPU utilization (for scaling)
resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name          = "webapp-cpu-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "Triggers scaling when CPU > 80% for 15 minutes"
  #alarm_actions       = [aws_autoscaling_policy.cpu_policy.arn, aws_sns_topic.cloudwatch_alarms.arn,]
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions         = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "ignore"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.app_asg.name
  }

  tags = merge(local.common_tags, {
    Name    = "webapp-cpu-utilization"
    Purpose = "AutoScaling"
  })
}



# Memory Utilization Alarm (Monitoring Only)
resource "aws_cloudwatch_metric_alarm" "memory_alarm" {
  alarm_name          = "bastion-memory-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "mem_used_percent"
  namespace           = "CWAgent"  # Must match SSM config
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "Triggers when memory > 80% for 15 minutes"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "notBreaching"  # Better for production

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
    
  }

  tags = merge(local.common_tags, {
    Name    = "bastion-memory-utilization",
    Purpose = "Monitoring"
  })
}


resource "aws_cloudwatch_metric_alarm" "webapp_memory_alarm" {
  alarm_name          = "webapp-memory-utilization"
  namespace           = "CWAgent"
  metric_name         = "mem_used_percent"
  
  dimensions = {
    InstanceId           = "i-02aeb0e1936dc6e90"
    AutoScalingGroupName = "cb365-webapp-asg"
    ImageId              = "ami-0dffca59de3f3529c"
  }

  comparison_operator = "GreaterThanThreshold"
  threshold           = 80  # Alert when memory usage exceeds 80%
  evaluation_periods  = 3   # 3 evaluation periods (15 minutes total)
  period              = 300 # 5 minutes in seconds
  statistic           = "Average"
  treat_missing_data  = "notBreaching" # Recommended for production

  alarm_description = "Alerts when memory usage exceeds 80% for 15 minutes on webapp instance"
  alarm_actions     = [aws_sns_topic.cloudwatch_alarms.arn] 
  ok_actions        = [aws_sns_topic.cloudwatch_alarms.arn]

  tags = merge(local.common_tags, {
    Name    = "app-disk-utilization"
    Purpose = "Monitoring"
  })
}




# Disk Usage Alarm (Monitoring Only)
resource "aws_cloudwatch_metric_alarm" "disk_alarm" {
  alarm_name          = "bastion-disk-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "disk_used_percent"
  namespace           = "CWAgent"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "Disk monitoring (does not trigger scaling) - 15 minute threshold"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions         = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "ignore"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
    path                = "/"
    fstype              = "ext4"
    device              = "xvda1"
  }

  tags = merge(local.common_tags, {
    Name    = "bastion-disk-utilization"
    Purpose = "Monitoring"
  })
}


resource "aws_cloudwatch_metric_alarm" "root_disk_usage_alarm" {
  alarm_name          = "webapp-root-disk-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 3               # 15 minutes (3 periods × 5 minutes)
  metric_name         = "disk_used_percent"
  namespace           = "CWAgent"
  period              = 300             # 5 minutes in seconds
  statistic           = "Average"
  threshold           = 85              # Alert when >85% used
  alarm_description   = "Root disk (/) usage exceeds 85% capacity on webapp instances"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "notBreaching"  # Recommended for production

  dimensions = {
    InstanceId           = "i-02aeb0e1936dc6e90"
    AutoScalingGroupName = "cb365-webapp-asg"
    ImageId              = "ami-0dffca59de3f3529c"
    path                 = "/"
    fstype               = "ext4"
  }

  tags = merge(local.common_tags, {
    Name          = "webapp-root-disk-alarm",
    MountPoint    = "/",
    Filesystem    = "ext4",
    Monitoring    = "true"
  })
}






# ========== UPDATED SSM PARAMETERS ==========


# SSM Parameters for CloudWatch Agent Configuration
resource "aws_ssm_parameter" "bastion_cw_agent_config" {
  name        = "/AmazonCloudWatch-bastion/config"
  description = "Enhanced config for bastion+nginx hosts"
  type        = "String"
  tier        = "Standard"
  value = jsonencode({
    "agent" : {
      "metrics_collection_interval" : 60,
      "run_as_user" : "cwagent",
      "debug" : false
    },
    "metrics" : {
      "namespace" : "Bastion/Nginx",
      "append_dimensions" : {
        "InstanceId" : "$${aws:InstanceId}",       
        "AutoScalingGroupName" : "$${aws:AutoScalingGroupName}"
      },
      "metrics_collected" : {
        "cpu" : {
          "measurement" : ["cpu_usage_user"],
          "totalcpu" : false,
          "metrics_collection_interval" : 60
        },
        "mem" : {
          "measurement" : ["mem_used_percent"],
          "metrics_collection_interval" : 60
        },
        "disk" : {
          "measurement" : ["disk_used_percent"],   
          "resources" : ["/", "/dev/xvda1"],
          "metrics_collection_interval" : 60
        },
        "nginx" : {
          "metrics_collection_interval" : 60,
          "nginx_status_url" : "http://localhost/nginx_status"
        }
      }
    },
    "logs" : {
      "logs_collected" : {
        "files" : {
          "collect_list" : [
            {
              "file_path" : "/var/log/nginx/access.log",
              "log_group_name" : "/bastion/nginx/access",
              "retention_in_days" : 7
            },
            {
              "file_path" : "/var/log/nginx/error.log",
              "log_group_name" : "/bastion/nginx/error",
              "retention_in_days" : 30
            }
          ]
        }
      }
    }
  })
}

resource "aws_ssm_parameter" "app_cw_agent_config" {
  name        = "/AmazonCloudWatch-app/config"
  description = "Optimized app server config"
  type        = "String"
  tier        = "Standard"
  value = jsonencode({
    "agent" : {
      "metrics_collection_interval" : 60,
      "run_as_user" : "cwagent"
    },
    "metrics" : {
      "namespace" : "AppServers",
      "metrics_collected" : {
        "cpu" : {
          "measurement" : ["cpu_usage_user"],
          "metrics_collection_interval" : 60
        },
        "mem" : {
          "measurement" : ["mem_used_percent"],
          "metrics_collection_interval" : 60
        },
        "disk" : {
          "measurement" : ["disk_used_percent"],
          "resources" : ["/", "/dev/xvda1"],
          "metrics_collection_interval" : 60
        }
      }
    }
  })
}


# ========== NEW NGINX ALARM ==========

# NGINX 5xx Errors Alarm
resource "aws_cloudwatch_metric_alarm" "nginx_5xx_alarm" {
  alarm_name          = "nginx-5xx-errors"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  namespace           = "Bastion/Nginx"
  metric_name         = "nginx_http_5xx"
  statistic           = "Sum"
  period              = 300
  evaluation_periods  = 1
  threshold           = 10
  alarm_description   = "Critical NGINX server errors detected"

  alarm_actions = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions    = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data = "notBreaching"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
  }

  tags = merge(local.common_tags, {
    Name    = "nginx-5xx-errors"
    Purpose = "Monitoring"
  })
}

# Disk Alarm for EFS Mount
resource "aws_cloudwatch_metric_alarm" "efs_disk_usage_alarm" {
  alarm_name          = "efs-nginx-disk-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 3               # 15 minutes (3 periods × 5 minutes)
  metric_name         = "disk_used_percent"
  namespace           = "CWAgent"
  period              = 300             # 5 minutes in seconds
  statistic           = "Average"
  threshold           = 85              # Alert when >85% used
  alarm_description   = "EFS mount /var/www/html/nginx usage exceeds 85% capacity"
  alarm_actions       = [aws_sns_topic.cloudwatch_alarms.arn]
  ok_actions          = [aws_sns_topic.cloudwatch_alarms.arn]
  treat_missing_data  = "notBreaching"  # Better for production than "ignore"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.bastion_asg.name
    path                 = "/var/www/html/nginx"
    device               = local.efs_mount_point
    fstype               = "nfs4"
  }

  tags = merge(local.common_tags, {
    Name          = "efs-nginx-disk-alarm"
  })
}






/

{
  "metrics": {
    "metrics_collected": {
      "mem": {
        "measurement": ["mem_used_percent"],
        "metrics_collection_interval": 60
      }
    },
    "append_dimensions": {
      "AutoScalingGroupName": "${aws:AutoScalingGroupName}"
    }
  }
}

{
  "metrics": {
    "metrics_collected": {
      "mem": {
        "measurement": ["mem_used_percent"],
        "metrics_collection_interval": 70,
        "append_dimensions": {
          "AutoScalingGroupName": "${aws:AutoScalingGroupName}"
        }
      },
      "disk": {
        "measurement": ["disk_used_percent"],
        "metrics_collection_interval": 70,
        "resources": ["/", "/var/www/html/nginx"],
        "ignore_file_system_types": ["tmpfs", "devtmpfs", "squashfs", "overlay"],
        "drop_device": true,
        "append_dimensions": {
          "AutoScalingGroupName": "${aws:AutoScalingGroupName}"
        }
      }
    },
    "append_dimensions": {
      "AutoScalingGroupName": "${aws:AutoScalingGroupName}",
      "InstanceId": "${aws:InstanceId}",
      "ImageId": "${aws:ImageId}"
    }
  }
}


{
  "metrics": {
    "metrics_collected": {
      "mem": {
        "measurement": ["mem_used_percent"],
        "metrics_collection_interval": 300,
        "append_dimensions": {
          "AutoScalingGroupName": "${aws:AutoScalingGroupName}"
        }
      },
      "disk": {
        "measurement": ["disk_used_percent"],
        "metrics_collection_interval": 300,
        "resources": ["/"],  
        "ignore_file_system_types": ["tmpfs", "devtmpfs", "squashfs", "overlay"],
        "drop_device": false, 
        "append_dimensions": {
          "AutoScalingGroupName": "${aws:AutoScalingGroupName}"
        }
      }
    },
    "append_dimensions": {
      "AutoScalingGroupName": "${aws:AutoScalingGroupName}",
      "InstanceId": "${aws:InstanceId}",
      "ImageId": "${aws:ImageId}"
    }
  }
}




*/