data "aws_ami" "ami" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["debian-12-amd64-*"]
  }

  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "architecture"
    values = ["x86_64"]
  }
}
# Availability Zones
data "aws_availability_zones" "available" {
  state = "available"
}

# Fetch EFS details
data "aws_efs_file_system" "web_storage" {
  tags = {
    Name = "cb365-web-efs"  # Your EFS tag name
  }
}