
# EFS File System for shared storage
resource "aws_efs_file_system" "shared" {
  creation_token   = "cb365-web-efs"
  performance_mode = "generalPurpose"
  encrypted        = true

  lifecycle_policy {
    transition_to_ia = "AFTER_30_DAYS"
  }

  tags = merge(local.common_tags, {
    Name = "cb365-web-efs"
  })
}

# Single EFS Mount Target for both subnets (since they're in the same AZ)
resource "aws_efs_mount_target" "mount_target" {
  file_system_id  = aws_efs_file_system.shared.id
  subnet_id       = aws_subnet.app_and_db_subnet.id
  security_groups = [aws_security_group.efs_sg.id]
}


