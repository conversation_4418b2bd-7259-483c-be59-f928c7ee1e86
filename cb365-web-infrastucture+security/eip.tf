
# Data source to get the instance ID
data "aws_instances" "bastion_instance" {
  depends_on = [aws_autoscaling_group.bastion_asg]

  filter {
    name   = "tag:Name"
    values = ["cb365-web-bastion-instance"]
  }

  filter {
    name   = "instance-state-name"
    values = ["running"]
  }
}


# Create EIP
resource "aws_eip" "bastion_host_eip" {
  domain = "vpc"
  tags = merge(local.common_tags, {
    Name = "cb365-web-eip"
  })
}


# EIP Association
resource "aws_eip_association" "bastion_eip_assoc" {
  depends_on = [
    data.aws_instances.bastion_instance,
    aws_eip.bastion_host_eip
  ]

  instance_id   = try(data.aws_instances.bastion_instance.ids[0], null)
  allocation_id = aws_eip.bastion_host_eip.id

  lifecycle {
    create_before_destroy = true
  }
}