# Use existing VPC instead of creating new one
data "aws_vpc" "main" {
  id = "vpc-0b59b064c7c1c4c64"  # Your existing cb365-prod-vpc
}


# Use existing public subnet for Bastion Host
data "aws_subnet" "bastion_host_subnet" {
  id = "subnet-05784d834a118cbee"  # Your existing cb365-prod-public-subnet-1 (********/24, eu-west-1a)
}


# Use existing private subnet for Application and Database
data "aws_subnet" "app_and_db_subnet" {
  id = "subnet-078a8ecbccb92cecb"  # Your existing cb365-prod-private-subnet-1 (*********/24, eu-west-1a)
}



# Use existing Internet Gateway
data "aws_internet_gateway" "igw" {
  filter {
    name   = "attachment.vpc-id"
    values = [data.aws_vpc.main.id]
  }
}


# Use existing public route table
data "aws_route_table" "public_rt" {
  route_table_id = "rtb-01899365ea90f8491"  # Your existing cb365-prod-public-rt
}

# Note: Route table associations already exist for the public subnets

# Use existing private route table
data "aws_route_table" "private_rt" {
  route_table_id = "rtb-0fe345c41647daee3"  # Your existing cb365-prod-private-rt
}

# Note: Route table associations already exist for the private subnets

# NAT Gateway configuration - Currently not present in existing VPC
# Uncomment and modify if you need NAT Gateway for private subnet internet access

# resource "aws_eip" "nat" {
#   domain     = "vpc"
#   depends_on = [data.aws_internet_gateway.igw]
#
#   tags = merge(local.common_tags, {
#     Name = "cb365-web-nat-eip"
#   })
# }

# resource "aws_nat_gateway" "nat" {
#   allocation_id = aws_eip.nat.id
#   subnet_id     = data.aws_subnet.bastion_host_subnet.id
#
#   depends_on = [aws_eip.nat]
#
#   tags = merge(local.common_tags, {
#     Name = "cb365-web-nat-gateway"
#   })
# }

# resource "aws_route" "private_nat_route" {
#   route_table_id         = data.aws_route_table.private_rt.id
#   destination_cidr_block = "0.0.0.0/0"
#   nat_gateway_id         = aws_nat_gateway.nat.id
#
#   depends_on = [aws_nat_gateway.nat]
# }





