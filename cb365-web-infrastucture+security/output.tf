output "web_public_ip" {
  description = "The public IP address of the web server"
  value       = aws_eip.bastion_host_eip.public_ip
  depends_on  = [aws_eip.bastion_host_eip]
}


output "mount_target_dns_name" {
  description = "The DNS name for the EFS mount target"
  value       = aws_efs_mount_target.mount_target.dns_name
}

output "efs_id" {
  description = "The ID of the EFS file system"
  value       = aws_efs_file_system.shared.id
}
