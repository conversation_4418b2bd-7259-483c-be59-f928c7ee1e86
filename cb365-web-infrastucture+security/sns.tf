# SNS Topic for CloudWatch Alarms
resource "aws_sns_topic" "cloudwatch_alarms" {
  name = "cloudwatch-alarms-topic"
  tags = local.common_tags
}

# SNS Topic Policy
resource "aws_sns_topic_policy" "cloudwatch_alarms" {
  arn = aws_sns_topic.cloudwatch_alarms.arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowCloudWatchAlarms"
        Effect = "Allow"
        Principal = {
          Service = "cloudwatch.amazonaws.com"
        }
        Action   = "SNS:Publish"
        Resource = aws_sns_topic.cloudwatch_alarms.arn
      }
    ]
  })
}

# Email Subscription using dynamic blocks
resource "aws_sns_topic_subscription" "email" {
  count     = length(var.alert_email_addresses)
  topic_arn = aws_sns_topic.cloudwatch_alarms.arn
  protocol  = "email"
  endpoint  = var.alert_email_addresses[count.index]
}

# SMS Subscription using dynamic blocks
resource "aws_sns_topic_subscription" "sms" {
  count     = length(var.alert_phone_numbers)
  topic_arn = aws_sns_topic.cloudwatch_alarms.arn
  protocol  = "sms"
  endpoint  = var.alert_phone_numbers[count.index]
}