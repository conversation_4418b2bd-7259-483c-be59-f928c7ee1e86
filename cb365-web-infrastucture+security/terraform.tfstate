{"version": 4, "terraform_version": "1.10.5", "serial": 415, "lineage": "e4477325-4aa1-dc22-8f25-91d7383f7a90", "outputs": {"efs_id": {"value": "fs-02ac6b86b17adc6c7", "type": "string"}, "mount_target_dns_name": {"value": "fs-02ac6b86b17adc6c7.efs.eu-west-1.amazonaws.com", "type": "string"}, "web_public_ip": {"value": "************", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_ami", "name": "ami", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architecture": "x86_64", "arn": "arn:aws:ec2:eu-west-1::image/ami-0fbb72557598f5284", "block_device_mappings": [{"device_name": "/dev/xvda", "ebs": {"delete_on_termination": "true", "encrypted": "false", "iops": "3000", "snapshot_id": "snap-03b57c6c7076dbb4c", "throughput": "125", "volume_size": "8", "volume_type": "gp3"}, "no_device": "", "virtual_name": ""}], "boot_mode": "", "creation_date": "2025-03-17T15:34:08.000Z", "deprecation_time": "2027-03-17T15:34:08.000Z", "description": "Debian 12 (20250316-2053)", "ena_support": true, "executable_users": null, "filter": [{"name": "architecture", "values": ["x86_64"]}, {"name": "name", "values": ["debian-12-amd64-*"]}, {"name": "root-device-type", "values": ["ebs"]}, {"name": "virtualization-type", "values": ["hvm"]}], "hypervisor": "xen", "id": "ami-0fbb72557598f5284", "image_id": "ami-0fbb72557598f5284", "image_location": "amazon/debian-12-amd64-20250316-2053", "image_owner_alias": "amazon", "image_type": "machine", "imds_support": "", "include_deprecated": false, "kernel_id": "", "most_recent": true, "name": "debian-12-amd64-20250316-2053", "name_regex": null, "owner_id": "136693071363", "owners": ["amazon"], "platform": "", "platform_details": "Linux/UNIX", "product_codes": [], "public": true, "ramdisk_id": "", "root_device_name": "/dev/xvda", "root_device_type": "ebs", "root_snapshot_id": "snap-03b57c6c7076dbb4c", "sriov_net_support": "simple", "state": "available", "state_reason": {"code": "UNSET", "message": "UNSET"}, "tags": {}, "timeouts": null, "tpm_support": "", "uefi_data": null, "usage_operation": "RunInstances", "virtualization_type": "hvm"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["eu-west-1-zg-1"], "id": "eu-west-1", "names": ["eu-west-1a", "eu-west-1b", "eu-west-1c"], "state": "available", "timeouts": null, "zone_ids": ["euw1-az1", "euw1-az2", "euw1-az3"]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_efs_file_system", "name": "web_storage", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticfilesystem:eu-west-1:************:file-system/fs-02ac6b86b17adc6c7", "availability_zone_id": "", "availability_zone_name": "", "creation_token": "cb365-web-efs", "dns_name": "fs-02ac6b86b17adc6c7.efs.eu-west-1.amazonaws.com", "encrypted": true, "file_system_id": "fs-02ac6b86b17adc6c7", "id": "fs-02ac6b86b17adc6c7", "kms_key_id": "arn:aws:kms:eu-west-1:************:key/43a7e77d-dda4-4820-8e44-3e5f4232b787", "lifecycle_policy": [{"transition_to_archive": "", "transition_to_ia": "AFTER_30_DAYS", "transition_to_primary_storage_class": ""}], "name": "cb365-web-efs", "performance_mode": "<PERSON><PERSON><PERSON><PERSON>", "protection": [{"replication_overwrite": "ENABLED"}], "provisioned_throughput_in_mibps": 0, "size_in_bytes": 122009600, "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-efs", "Project": "cb365-web-infrastructure"}, "throughput_mode": "bursting"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_instances", "name": "bastion_instance", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"filter": [{"name": "instance-state-name", "values": ["running"]}, {"name": "tag:Name", "values": ["cb365-web-bastion-instance"]}], "id": "eu-west-1", "ids": ["i-06b279609ad082501"], "instance_state_names": null, "instance_tags": null, "ipv6_addresses": [], "private_ips": ["**********"], "public_ips": ["*************"], "timeouts": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_autoscaling_group", "name": "app_asg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:autoscaling:eu-west-1:************:autoScalingGroup:83da2553-df25-4e3a-a9cb-d61d91af0013:autoScalingGroupName/cb365-webapp-asg", "availability_zone_distribution": [{"capacity_distribution_strategy": "balanced-best-effort"}], "availability_zones": ["eu-west-1a"], "capacity_rebalance": false, "context": "", "default_cooldown": 300, "default_instance_warmup": 0, "desired_capacity": 1, "desired_capacity_type": "", "enabled_metrics": ["GroupAndWarmPoolDesiredCapacity", "GroupAndWarmPoolTotalCapacity", "GroupDesiredCapacity", "GroupInServiceCapacity", "GroupInServiceInstances", "GroupMaxSize", "GroupMinSize", "GroupPendingCapacity", "GroupPendingInstances", "GroupStandbyCapacity", "GroupStandbyInstances", "GroupTerminatingCapacity", "GroupTerminatingInstances", "GroupTotalCapacity", "GroupTotalInstances", "WarmPoolDesiredCapacity", "WarmPoolMinSize", "WarmPoolPendingCapacity", "WarmPoolTerminatingCapacity", "WarmPoolTotalCapacity", "WarmPoolWarmedCapacity"], "force_delete": true, "force_delete_warm_pool": false, "health_check_grace_period": 300, "health_check_type": "EC2", "id": "cb365-webapp-asg", "ignore_failed_scaling_activities": false, "initial_lifecycle_hook": [], "instance_maintenance_policy": [], "instance_refresh": [{"preferences": [{"alarm_specification": [], "auto_rollback": false, "checkpoint_delay": "", "checkpoint_percentages": [], "instance_warmup": "300", "max_healthy_percentage": 100, "min_healthy_percentage": 0, "scale_in_protected_instances": "Ignore", "skip_matching": false, "standby_instances": "Ignore"}], "strategy": "Rolling", "triggers": []}], "launch_configuration": "", "launch_template": [{"id": "lt-01378882d785a894e", "name": "cb365-web-app-instance20250326191520623200000004", "version": "$Latest"}], "load_balancers": [], "max_instance_lifetime": 0, "max_size": 2, "metrics_granularity": "1Minute", "min_elb_capacity": null, "min_size": 1, "mixed_instances_policy": [], "name": "cb365-webapp-asg", "name_prefix": "", "placement_group": "", "predicted_capacity": 0, "protect_from_scale_in": false, "service_linked_role_arn": "arn:aws:iam::************:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling", "suspended_processes": [], "tag": [{"key": "Backup", "propagate_at_launch": true, "value": "true"}, {"key": "ManagedBy", "propagate_at_launch": true, "value": "Terraform"}, {"key": "Name", "propagate_at_launch": true, "value": "cb365-webapp-asg"}, {"key": "Project", "propagate_at_launch": true, "value": "cb365-web-infrastructure"}, {"key": "Role", "propagate_at_launch": true, "value": "AppServer"}], "target_group_arns": [], "termination_policies": [], "timeouts": null, "traffic_source": [], "vpc_zone_identifier": ["subnet-07fe6a511ecaf983f"], "wait_for_capacity_timeout": "10m", "wait_for_elb_capacity": null, "warm_pool": [], "warm_pool_size": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjo2MDAwMDAwMDAwMDAsInVwZGF0ZSI6NjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_subnet.app_and_db_subnet", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_autoscaling_group", "name": "bastion_asg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:autoscaling:eu-west-1:************:autoScalingGroup:d07e985d-40fd-494c-9f41-56f745cd3303:autoScalingGroupName/bastion-asg-20250326191650707900000009", "availability_zone_distribution": [{"capacity_distribution_strategy": "balanced-best-effort"}], "availability_zones": ["eu-west-1a"], "capacity_rebalance": false, "context": "", "default_cooldown": 300, "default_instance_warmup": 0, "desired_capacity": 1, "desired_capacity_type": "", "enabled_metrics": ["GroupAndWarmPoolDesiredCapacity", "GroupAndWarmPoolTotalCapacity", "GroupDesiredCapacity", "GroupInServiceCapacity", "GroupInServiceInstances", "GroupMaxSize", "GroupMinSize", "GroupPendingCapacity", "GroupPendingInstances", "GroupStandbyCapacity", "GroupStandbyInstances", "GroupTerminatingCapacity", "GroupTerminatingInstances", "GroupTotalCapacity", "GroupTotalInstances", "WarmPoolDesiredCapacity", "WarmPoolMinSize", "WarmPoolPendingCapacity", "WarmPoolTerminatingCapacity", "WarmPoolTotalCapacity", "WarmPoolWarmedCapacity"], "force_delete": true, "force_delete_warm_pool": false, "health_check_grace_period": 300, "health_check_type": "EC2", "id": "bastion-asg-20250326191650707900000009", "ignore_failed_scaling_activities": false, "initial_lifecycle_hook": [], "instance_maintenance_policy": [], "instance_refresh": [{"preferences": [{"alarm_specification": [], "auto_rollback": false, "checkpoint_delay": "", "checkpoint_percentages": [], "instance_warmup": "300", "max_healthy_percentage": 100, "min_healthy_percentage": 0, "scale_in_protected_instances": "Ignore", "skip_matching": false, "standby_instances": "Ignore"}], "strategy": "Rolling", "triggers": []}], "launch_configuration": "", "launch_template": [{"id": "lt-072e7cda0719a3129", "name": "bastion-template-20250326191642529600000007", "version": "$Latest"}], "load_balancers": [], "max_instance_lifetime": 0, "max_size": 1, "metrics_granularity": "1Minute", "min_elb_capacity": null, "min_size": 1, "mixed_instances_policy": [], "name": "bastion-asg-20250326191650707900000009", "name_prefix": "bastion-asg-", "placement_group": "", "predicted_capacity": 0, "protect_from_scale_in": false, "service_linked_role_arn": "arn:aws:iam::************:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling", "suspended_processes": [], "tag": [{"key": "Backup", "propagate_at_launch": true, "value": "true"}, {"key": "ManagedBy", "propagate_at_launch": true, "value": "Terraform"}, {"key": "Name", "propagate_at_launch": true, "value": "cb365-web-bastion-instance"}, {"key": "Project", "propagate_at_launch": true, "value": "cb365-web-infrastructure"}], "target_group_arns": [], "termination_policies": [], "timeouts": null, "traffic_source": [], "vpc_zone_identifier": ["subnet-01a2fcf09d6258314"], "wait_for_capacity_timeout": "10m", "wait_for_elb_capacity": null, "warm_pool": [], "warm_pool_size": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjo2MDAwMDAwMDAwMDAsInVwZGF0ZSI6NjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_autoscaling_group.app_asg", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_launch_template.bastion_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_autoscaling_policy", "name": "cpu_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"adjustment_type": "", "arn": "arn:aws:autoscaling:eu-west-1:************:scalingPolicy:af8dde9b-1fc8-4ba0-8e4c-c60139591ce5:autoScalingGroupName/cb365-webapp-asg:policyName/webapp-cpu-utilization", "autoscaling_group_name": "cb365-webapp-asg", "cooldown": 0, "enabled": true, "estimated_instance_warmup": 300, "id": "webapp-cpu-utilization", "metric_aggregation_type": "", "min_adjustment_magnitude": 0, "name": "webapp-cpu-utilization", "policy_type": "TargetTrackingScaling", "predictive_scaling_configuration": [], "scaling_adjustment": 0, "step_adjustment": [], "target_tracking_configuration": [{"customized_metric_specification": [], "disable_scale_in": false, "predefined_metric_specification": [{"predefined_metric_type": "ASGAverageCPUUtilization", "resource_label": ""}], "target_value": 80}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_subnet.app_and_db_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_autoscaling_policy", "name": "memory_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"adjustment_type": "", "arn": "arn:aws:autoscaling:eu-west-1:************:scalingPolicy:f496ab60-3d01-4792-8256-f26d04190696:autoScalingGroupName/cb365-webapp-asg:policyName/memory-tracking-policy", "autoscaling_group_name": "cb365-webapp-asg", "cooldown": 0, "enabled": true, "estimated_instance_warmup": 0, "id": "memory-tracking-policy", "metric_aggregation_type": "", "min_adjustment_magnitude": 0, "name": "memory-tracking-policy", "policy_type": "TargetTrackingScaling", "predictive_scaling_configuration": [], "scaling_adjustment": 0, "step_adjustment": [], "target_tracking_configuration": [{"customized_metric_specification": [{"metric_dimension": [{"name": "AutoScalingGroupName", "value": "cb365-webapp-asg"}], "metric_name": "MemoryUtilization", "metrics": [], "namespace": "AWS/EC2", "statistic": "Average", "unit": ""}], "disable_scale_in": false, "predefined_metric_specification": [], "target_value": 70}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_subnet.app_and_db_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_dashboard", "name": "bastion_memory_dashboard", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dashboard_arn": "arn:aws:cloudwatch::************:dashboard/Bastion-Memory-Dashboard", "dashboard_body": "{\"widgets\":[{\"height\":6,\"properties\":{\"annotations\":{\"horizontal\":[{\"color\":\"#ff0000\",\"label\":\"Alarm Threshold (80%)\",\"value\":80}]},\"metrics\":[[\"CWAgent\",\"mem_used_percent\",\"AutoScalingGroupName\",\"bastion-asg-20250326191650707900000009\",{\"label\":\"Max Memory %\",\"stat\":\"Maximum\"}],[\"...\",{\"label\":\"Avg Memory %\",\"stat\":\"Average\"}],[\"...\",{\"label\":\"Min Memory %\",\"stat\":\"Minimum\"}]],\"period\":300,\"region\":\"eu-west-1\",\"stacked\":false,\"title\":\"Bastion Memory Utilization (%)\",\"view\":\"timeSeries\"},\"type\":\"metric\",\"width\":12,\"x\":0,\"y\":0},{\"height\":6,\"properties\":{\"alarms\":[\"arn:aws:cloudwatch:eu-west-1:************:alarm:bastion-memory-utilization\"],\"title\":\"Memory Alarm Status\"},\"type\":\"alarm\",\"width\":12,\"x\":12,\"y\":0},{\"height\":6,\"properties\":{\"metrics\":[[\"CWAgent\",\"swap_used_percent\",\"AutoScalingGroupName\",\"bastion-asg-20250326191650707900000009\",{\"label\":\"Swap Used %\"}]],\"period\":300,\"region\":\"eu-west-1\",\"title\":\"Swap Usage (%)\",\"view\":\"timeSeries\"},\"type\":\"metric\",\"width\":12,\"x\":0,\"y\":6},{\"height\":6,\"properties\":{\"metrics\":[[\"AWS/EC2\",\"CPUUtilization\",\"AutoScalingGroupName\",\"bastion-asg-20250326191650707900000009\",{\"label\":\"CPU %\"}],[\"CWAgent\",\"disk_used_percent\",\"AutoScalingGroupName\",\"bastion-asg-20250326191650707900000009\",{\"label\":\"Disk Used %\"}]],\"period\":300,\"region\":\"eu-west-1\",\"stacked\":false,\"title\":\"CPU & Disk Utilization\",\"view\":\"timeSeries\"},\"type\":\"metric\",\"width\":12,\"x\":12,\"y\":6}]}", "dashboard_name": "Bastion-Memory-Dashboard", "id": "Bastion-Memory-Dashboard"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_autoscaling_group.bastion_asg", "aws_cloudwatch_metric_alarm.memory_alarm", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_launch_template.bastion_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_dashboard", "name": "bastion_performance_dashboard", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dashboard_arn": "arn:aws:cloudwatch::************:dashboard/Bastion-Performance-Dashboard", "dashboard_body": "{\"widgets\":[{\"height\":6,\"properties\":{\"annotations\":{\"horizontal\":[{\"color\":\"#ff0000\",\"label\":\"Alarm Threshold (80%)\",\"value\":80}]},\"metrics\":[[\"AWS/EC2\",\"CPUUtilization\",\"AutoScalingGroupName\",\"bastion-asg-20250326191650707900000009\",{\"label\":\"Average CPU %\",\"stat\":\"Average\"}],[\"...\",{\"label\":\"Max CPU %\",\"stat\":\"Maximum\"}],[\"...\",{\"label\":\"Min CPU %\",\"stat\":\"Minimum\"}]],\"period\":300,\"region\":\"eu-west-1\",\"stacked\":false,\"title\":\"Bastion CPU Utilization\",\"view\":\"timeSeries\"},\"type\":\"metric\",\"width\":12,\"x\":0,\"y\":0},{\"height\":6,\"properties\":{\"alarms\":[\"arn:aws:cloudwatch:eu-west-1:************:alarm:bastion-cpu-utilization\"],\"title\":\"Alarm Status\"},\"type\":\"alarm\",\"width\":12,\"x\":12,\"y\":0},{\"height\":6,\"properties\":{\"metrics\":[[\"AWS/AutoScaling\",\"GroupInServiceInstances\",\"AutoScalingGroupName\",\"bastion-asg-20250326191650707900000009\"]],\"period\":300,\"region\":\"eu-west-1\",\"title\":\"Bastion Instance Count\",\"view\":\"singleValue\"},\"type\":\"metric\",\"width\":12,\"x\":0,\"y\":6},{\"height\":6,\"properties\":{\"metrics\":[[\"AWS/EC2\",\"NetworkIn\",\"AutoScalingGroupName\",\"bastion-asg-20250326191650707900000009\"],[\"...\",\"NetworkOut\"]],\"period\":300,\"region\":\"eu-west-1\",\"stacked\":false,\"title\":\"Network Performance (Bytes)\",\"view\":\"timeSeries\"},\"type\":\"metric\",\"width\":12,\"x\":12,\"y\":6}]}", "dashboard_name": "Bastion-Performance-Dashboard", "id": "Bastion-Performance-Dashboard"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_autoscaling_group.bastion_asg", "aws_cloudwatch_metric_alarm.cpu_alarm", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_launch_template.bastion_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "vpc_flow_log", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:eu-west-1:************:log-group:/aws/vpc/flow-logs", "id": "/aws/vpc/flow-logs", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/vpc/flow-logs", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-vpc-flow-logs", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-vpc-flow-logs", "Project": "cb365-web-infrastructure"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "app_disk_alarm", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Disk monitoring (does not trigger scaling) - 15 minute threshold", "alarm_name": "webapp-disk-utilization", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:webapp-disk-utilization", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 0, "dimensions": {"AutoScalingGroupName": "cb365-webapp-asg", "device": "xvda1", "fstype": "ext4", "path": "/"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 3, "extended_statistic": "", "id": "webapp-disk-utilization", "insufficient_data_actions": null, "metric_name": "disk_used_percent", "metric_query": [], "namespace": "CWAgent", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 300, "statistic": "Maximum", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "webapp-disk-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "webapp-disk-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "threshold": 85, "threshold_metric_id": "", "treat_missing_data": "notBreaching", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "app_memory_alarm", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Triggers when memory usage exceeds 80% in App ASG", "alarm_name": "app-memory-utilization", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:app-memory-utilization", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 3, "dimensions": {"AutoScalingGroupName": "cb365-webapp-asg"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 3, "extended_statistic": "", "id": "app-memory-utilization", "insufficient_data_actions": null, "metric_name": "mem_used_percent", "metric_query": [], "namespace": "CWAgent", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 300, "statistic": "Maximum", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "app-memory-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "app-memory-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "notBreaching", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "cpu_alarm", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Alarm when CPU exceeds 80% for 15 minutes", "alarm_name": "bastion-cpu-utilization", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:bastion-cpu-utilization", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 0, "dimensions": {"AutoScalingGroupName": "bastion-asg-20250326191650707900000009"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 3, "extended_statistic": "", "id": "bastion-cpu-utilization", "insufficient_data_actions": [], "metric_name": "CPUUtilization", "metric_query": [], "namespace": "AWS/EC2", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 300, "statistic": "Sum", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "bastion-cpu-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "bastion-cpu-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "ignore", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_autoscaling_group.bastion_asg", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_launch_template.bastion_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "disk_alarm", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Disk monitoring (does not trigger scaling) - 15 minute threshold", "alarm_name": "bastion-disk-utilization", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:bastion-disk-utilization", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 0, "dimensions": {"AutoScalingGroupName": "bastion-asg-20250326191650707900000009", "device": "xvda1", "fstype": "ext4", "path": "/"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 3, "extended_statistic": "", "id": "bastion-disk-utilization", "insufficient_data_actions": null, "metric_name": "disk_used_percent", "metric_query": [], "namespace": "CWAgent", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 300, "statistic": "Average", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "bastion-disk-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "bastion-disk-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "notBreaching", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_autoscaling_group.bastion_asg", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_launch_template.bastion_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "efs_ia_access_alarm", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Alarm when EFS IA is accessed (MeteredIOBytes > 0)", "alarm_name": "efs-infrequent-access-usage", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:efs-infrequent-access-usage", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 0, "dimensions": {"FileSystemId": "fs-02ac6b86b17adc6c7", "StorageClass": "Standard"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 1, "extended_statistic": "", "id": "efs-infrequent-access-usage", "insufficient_data_actions": null, "metric_name": "MeteredIOBytes", "metric_query": [], "namespace": "AWS/EFS", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 300, "statistic": "Sum", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "efs-ia-access-alarm", "Project": "cb365-web-infrastructure", "Purpose": "Detect EFS IA request charges"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "efs-ia-access-alarm", "Project": "cb365-web-infrastructure", "Purpose": "Detect EFS IA request charges"}, "threshold": 0, "threshold_metric_id": "", "treat_missing_data": "ignore", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_efs_file_system.shared", "aws_sns_topic.cloudwatch_alarms"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "efs_usage_percent_alarm", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Alarm when EFS usage exceeds 80% of 100 GiB", "alarm_name": "efs-usage-percent-alarm", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:efs-usage-percent-alarm", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 0, "dimensions": null, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 3, "extended_statistic": "", "id": "efs-usage-percent-alarm", "insufficient_data_actions": null, "metric_name": "", "metric_query": [{"account_id": "", "expression": "", "id": "m1", "label": "", "metric": [{"dimensions": {"FileSystemId": "fs-02ac6b86b17adc6c7", "StorageClass": "Total"}, "metric_name": "StorageBytes", "namespace": "AWS/EFS", "period": 300, "stat": "Average", "unit": ""}], "period": null, "return_data": false}, {"account_id": "", "expression": "m1 / ************ * 100", "id": "e1", "label": "EFS Usage Percent", "metric": [], "period": null, "return_data": true}], "namespace": "", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 0, "statistic": "", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "efs-usage-percent-alarm", "Project": "cb365-web-infrastructure", "Purpose": "Detect high EFS storage usage"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "efs-usage-percent-alarm", "Project": "cb365-web-infrastructure", "Purpose": "Detect high EFS storage usage"}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "ignore", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_efs_file_system.shared", "aws_sns_topic.cloudwatch_alarms"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "high_cpu_alert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Alert when average CPU > 80% for 15 minutes across instances in ASG", "alarm_name": "webapp-cpu-utilization-alert", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:webapp-cpu-utilization-alert", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 3, "dimensions": {"AutoScalingGroupName": "cb365-webapp-asg"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 3, "extended_statistic": "", "id": "webapp-cpu-utilization-alert", "insufficient_data_actions": null, "metric_name": "CPUUtilization", "metric_query": [], "namespace": "AWS/EC2", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 300, "statistic": "Average", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "webapp-cpu-utilization-alert", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "webapp-cpu-utilization-alert", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "ignore", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "high_memory", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:autoscaling:eu-west-1:************:scalingPolicy:f496ab60-3d01-4792-8256-f26d04190696:autoScalingGroupName/cb365-webapp-asg:policyName/memory-tracking-policy"], "alarm_description": "This metric monitors EC2 memory utilization", "alarm_name": "high-memory-utilization", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:high-memory-utilization", "comparison_operator": "GreaterThanThreshold", "datapoints_to_alarm": 0, "dimensions": {"AutoScalingGroupName": "cb365-webapp-asg"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 2, "extended_statistic": "", "id": "high-memory-utilization", "insufficient_data_actions": null, "metric_name": "MemoryUtilization", "metric_query": [], "namespace": "AWS/EC2", "ok_actions": null, "period": 300, "statistic": "Average", "tags": null, "tags_all": {}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "missing", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_autoscaling_policy.memory_policy", "aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_subnet.app_and_db_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "memory_alarm", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "alarm_description": "Triggers when memory > 80% for 15 minutes", "alarm_name": "bastion-memory-utilization", "arn": "arn:aws:cloudwatch:eu-west-1:************:alarm:bastion-memory-utilization", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 3, "dimensions": {"AutoScalingGroupName": "bastion-asg-20250326191650707900000009"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 3, "extended_statistic": "", "id": "bastion-memory-utilization", "insufficient_data_actions": [], "metric_name": "mem_used_percent", "metric_query": [], "namespace": "CWAgent", "ok_actions": ["arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"], "period": 300, "statistic": "Maximum", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "bastion-memory-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "bastion-memory-utilization", "Project": "cb365-web-infrastructure", "Purpose": "Monitoring"}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "notBreaching", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_autoscaling_group.bastion_asg", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_launch_template.bastion_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_sns_topic.cloudwatch_alarms", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_efs_file_system", "name": "shared", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticfilesystem:eu-west-1:************:file-system/fs-02ac6b86b17adc6c7", "availability_zone_id": "", "availability_zone_name": "", "creation_token": "cb365-web-efs", "dns_name": "fs-02ac6b86b17adc6c7.efs.eu-west-1.amazonaws.com", "encrypted": true, "id": "fs-02ac6b86b17adc6c7", "kms_key_id": "arn:aws:kms:eu-west-1:************:key/43a7e77d-dda4-4820-8e44-3e5f4232b787", "lifecycle_policy": [{"transition_to_archive": "", "transition_to_ia": "AFTER_30_DAYS", "transition_to_primary_storage_class": ""}], "name": "cb365-web-efs", "number_of_mount_targets": 1, "owner_id": "************", "performance_mode": "<PERSON><PERSON><PERSON><PERSON>", "protection": [{"replication_overwrite": "ENABLED"}], "provisioned_throughput_in_mibps": 0, "size_in_bytes": [{"value": 122009600, "value_in_ia": 0, "value_in_standard": 122009600}], "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-efs", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-efs", "Project": "cb365-web-infrastructure"}, "throughput_mode": "bursting"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_efs_mount_target", "name": "mount_target", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone_id": "euw1-az1", "availability_zone_name": "eu-west-1a", "dns_name": "fs-02ac6b86b17adc6c7.efs.eu-west-1.amazonaws.com", "file_system_arn": "arn:aws:elasticfilesystem:eu-west-1:************:file-system/fs-02ac6b86b17adc6c7", "file_system_id": "fs-02ac6b86b17adc6c7", "id": "fsmt-095a7422db62b773a", "ip_address": "*********", "mount_target_dns_name": "eu-west-1a.fs-02ac6b86b17adc6c7.efs.eu-west-1.amazonaws.com", "network_interface_id": "eni-04cecfd6bf83156d8", "owner_id": "************", "security_groups": ["sg-088e4fea95b6e6752"], "subnet_id": "subnet-07fe6a511ecaf983f", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_efs_file_system.shared", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_subnet.app_and_db_subnet", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_eip", "name": "bastion_host_eip", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-01206ed545e7851ad", "arn": "arn:aws:ec2:eu-west-1:************:elastic-ip/eipalloc-01206ed545e7851ad", "associate_with_private_ip": null, "association_id": "eipassoc-0a07436549111c8d2", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-01206ed545e7851ad", "instance": "i-04ce8ded087323abc", "ipam_pool_id": null, "network_border_group": "eu-west-1", "network_interface": "eni-0f52e11d6573ccbed", "private_dns": "ip-10-0-0-209.eu-west-1.compute.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-63-32-20-198.eu-west-1.compute.amazonaws.com", "public_ip": "************", "public_ipv4_pool": "amazon", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-eip", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-eip", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-0340cbaeeaa8ea8cf", "arn": "arn:aws:ec2:eu-west-1:************:elastic-ip/eipalloc-0340cbaeeaa8ea8cf", "associate_with_private_ip": null, "association_id": "", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-0340cbaeeaa8ea8cf", "instance": "", "ipam_pool_id": null, "network_border_group": "eu-west-1", "network_interface": "", "private_dns": null, "private_ip": "", "ptr_record": "", "public_dns": "ec2-18-202-103-85.eu-west-1.compute.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-nat-eip", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-nat-eip", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_internet_gateway.igw", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_eip_association", "name": "bastion_eip_assoc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allocation_id": "eipalloc-01206ed545e7851ad", "allow_reassociation": null, "id": "eipassoc-043d1e31aa927baf5", "instance_id": "i-06b279609ad082501", "network_interface_id": "eni-0db7416296a1c2f36", "private_ip_address": "**********", "public_ip": "************"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_autoscaling_group.bastion_asg", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_launch_template.bastion_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main", "data.aws_instances.bastion_instance"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_flow_log", "name": "vpc_flow_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:vpc-flow-log/fl-0eb9148c439ecf3e9", "deliver_cross_account_role": "", "destination_options": [], "eni_id": null, "iam_role_arn": "arn:aws:iam::************:role/vpc-flow-log-role", "id": "fl-0eb9148c439ecf3e9", "log_destination": "arn:aws:logs:eu-west-1:************:log-group:/aws/vpc/flow-logs", "log_destination_type": "cloud-watch-logs", "log_format": "${version} ${account-id} ${interface-id} ${srcaddr} ${dstaddr} ${srcport} ${dstport} ${protocol} ${packets} ${bytes} ${start} ${end} ${action} ${log-status}", "log_group_name": "/aws/vpc/flow-logs", "max_aggregation_interval": 600, "subnet_id": null, "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-vpc-flow-logs", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-vpc-flow-logs", "Project": "cb365-web-infrastructure"}, "traffic_type": "ALL", "transit_gateway_attachment_id": null, "transit_gateway_id": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudwatch_log_group.vpc_flow_log", "aws_iam_role.vpc_flow_log_role", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_iam_instance_profile", "name": "instance_profile", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:instance-profile/cb365-web-instance-profile", "create_date": "2025-03-26T19:14:55Z", "id": "cb365-web-instance-profile", "name": "cb365-web-instance-profile", "name_prefix": "", "path": "/", "role": "cb365-web-instance-role", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-instance-profile", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-instance-profile", "Project": "cb365-web-infrastructure"}, "unique_id": "AIPAYS2NT7ILCX7H7XCU2"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role", "name": "instance_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/cb365-web-instance-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-03-26T19:14:53Z", "description": "", "force_detach_policies": false, "id": "cb365-web-instance-role", "inline_policy": [{"name": "cb365-autoscaling-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"autoscaling:DescribeAutoScalingGroups\",\"autoscaling:DescribeAutoScalingInstances\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}, {"name": "cb365-cloudwatch-agent-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"cloudwatch:PutMetricData\",\"cloudwatch:GetMetricStatistics\",\"cloudwatch:GetMetricData\",\"cloudwatch:ListMetrics\",\"ec2:DescribeTags\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}, {"name": "cb365-cloudwatch-logs-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\",\"logs:DescribeLogStreams\",\"logs:PutRetentionPolicy\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"}]}"}, {"name": "cb365-ec2-networking-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ec2:AssociateAddress\",\"ec2:DisassociateAddress\",\"ec2:DescribeAddresses\",\"ec2:DescribeInstances\",\"ec2:DescribeTags\",\"ec2:DescribeNetworkInterfaces\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}, {"name": "cb365-efs-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"elasticfilesystem:ClientMount\",\"elasticfilesystem:ClientWrite\",\"elasticfilesystem:ClientRootAccess\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:elasticfilesystem:eu-west-1:************:file-system/fs-02ac6b86b17adc6c7\"},{\"Action\":[\"elasticfilesystem:DescribeFileSystems\",\"elasticfilesystem:DescribeMountTargets\",\"elasticfilesystem:DescribeMountTargetSecurityGroups\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}, {"name": "cb365-ssm-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssm:GetParameter\",\"ssm:PutParameter\",\"ssm:DescribeParameters\",\"ssm:GetParameters\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:ssm:*:*:parameter/AmazonCloudWatch-*\"}]}"}], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore", "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"], "max_session_duration": 3600, "name": "cb365-web-instance-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-instance-role", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-instance-role", "Project": "cb365-web-infrastructure"}, "unique_id": "AROAYS2NT7ILDY7THNX5Y"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role", "name": "vpc_flow_log_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/vpc-flow-log-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"vpc-flow-logs.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-03-26T19:14:53Z", "description": "", "force_detach_policies": false, "id": "vpc-flow-log-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "vpc-flow-log-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-vpc-flow-log-role", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-vpc-flow-log-role", "Project": "cb365-web-infrastructure"}, "unique_id": "AROAYS2NT7ILI6Y3HITYQ"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "autoscaling_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role:cb365-autoscaling-policy", "name": "cb365-autoscaling-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"autoscaling:DescribeAutoScalingGroups\",\"autoscaling:DescribeAutoScalingInstances\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "cloudwatch_agent_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role:cb365-cloudwatch-agent-policy", "name": "cb365-cloudwatch-agent-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"cloudwatch:PutMetricData\",\"cloudwatch:GetMetricStatistics\",\"cloudwatch:GetMetricData\",\"cloudwatch:ListMetrics\",\"ec2:DescribeTags\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "cloudwatch_logs_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role:cb365-cloudwatch-logs-policy", "name": "cb365-cloudwatch-logs-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\",\"logs:DescribeLogStreams\",\"logs:PutRetentionPolicy\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"}]}", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "ec2_networking_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role:cb365-ec2-networking-policy", "name": "cb365-ec2-networking-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ec2:AssociateAddress\",\"ec2:DisassociateAddress\",\"ec2:DescribeAddresses\",\"ec2:DescribeInstances\",\"ec2:DescribeTags\",\"ec2:DescribeNetworkInterfaces\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "efs_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role:cb365-efs-policy", "name": "cb365-efs-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"elasticfilesystem:ClientMount\",\"elasticfilesystem:ClientWrite\",\"elasticfilesystem:ClientRootAccess\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:elasticfilesystem:eu-west-1:************:file-system/fs-02ac6b86b17adc6c7\"},{\"Action\":[\"elasticfilesystem:DescribeFileSystems\",\"elasticfilesystem:DescribeMountTargets\",\"elasticfilesystem:DescribeMountTargetSecurityGroups\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_efs_file_system.shared", "aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "ssm_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role:cb365-ssm-policy", "name": "cb365-ssm-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssm:GetParameter\",\"ssm:PutParameter\",\"ssm:DescribeParameters\",\"ssm:GetParameters\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:ssm:*:*:parameter/AmazonCloudWatch-*\"}]}", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "vpc_flow_log_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "vpc-flow-log-role:vpc-flow-log-policy", "name": "vpc-flow-log-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\",\"logs:DescribeLogGroups\",\"logs:DescribeLogStreams\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "vpc-flow-log-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.vpc_flow_log_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "cloudwatch_agent", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role-20250418050714416400000002", "policy_arn": "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ssm_core", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "cb365-web-instance-role-20250418050714395800000001", "policy_arn": "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore", "role": "cb365-web-instance-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.instance_role"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "igw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:internet-gateway/igw-0d362cb1bb52bd2c5", "id": "igw-0d362cb1bb52bd2c5", "owner_id": "************", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-internet-gateway", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-internet-gateway", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_launch_template", "name": "app_template", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:launch-template/lt-01378882d785a894e", "block_device_mappings": [{"device_name": "/dev/xvda", "ebs": [{"delete_on_termination": "", "encrypted": "true", "iops": 0, "kms_key_id": "", "snapshot_id": "", "throughput": 0, "volume_size": 15, "volume_type": "gp3"}], "no_device": "", "virtual_name": ""}], "capacity_reservation_specification": [], "cpu_options": [], "credit_specification": [], "default_version": 1, "description": "", "disable_api_stop": false, "disable_api_termination": false, "ebs_optimized": "", "elastic_gpu_specifications": [], "elastic_inference_accelerator": [], "enclave_options": [], "hibernation_options": [], "iam_instance_profile": [{"arn": "", "name": "cb365-web-instance-profile"}], "id": "lt-01378882d785a894e", "image_id": "ami-0dffca59de3f3529c", "instance_initiated_shutdown_behavior": "", "instance_market_options": [], "instance_requirements": [], "instance_type": "t2.small", "kernel_id": "", "key_name": "test-s", "latest_version": 2, "license_specification": [], "maintenance_options": [], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "", "http_put_response_hop_limit": 2, "http_tokens": "required", "instance_metadata_tags": ""}], "monitoring": [{"enabled": true}], "name": "cb365-web-app-instance20250326191520623200000004", "name_prefix": "cb365-web-app-instance", "network_interfaces": [{"associate_carrier_ip_address": "", "associate_public_ip_address": "false", "connection_tracking_specification": [], "delete_on_termination": "", "description": "", "device_index": 0, "interface_type": "", "ipv4_address_count": 0, "ipv4_addresses": [], "ipv4_prefix_count": 0, "ipv4_prefixes": [], "ipv6_address_count": 0, "ipv6_addresses": [], "ipv6_prefix_count": 0, "ipv6_prefixes": [], "network_card_index": 0, "network_interface_id": "", "primary_ipv6": "", "private_ip_address": "", "security_groups": ["sg-053ee0368af8facde"], "subnet_id": "subnet-07fe6a511ecaf983f"}], "placement": [], "private_dns_name_options": [], "ram_disk_id": "", "security_group_names": [], "tag_specifications": [], "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-webapp-instance", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-webapp-instance", "Project": "cb365-web-infrastructure"}, "update_default_version": null, "user_data": "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", "vpc_security_group_ids": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_efs_file_system.shared", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_subnet.app_and_db_subnet", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_launch_template", "name": "bastion_template", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:launch-template/lt-072e7cda0719a3129", "block_device_mappings": [{"device_name": "/dev/xvda", "ebs": [{"delete_on_termination": "", "encrypted": "true", "iops": 0, "kms_key_id": "", "snapshot_id": "", "throughput": 0, "volume_size": 8, "volume_type": "gp3"}], "no_device": "", "virtual_name": ""}], "capacity_reservation_specification": [], "cpu_options": [], "credit_specification": [], "default_version": 1, "description": "", "disable_api_stop": false, "disable_api_termination": false, "ebs_optimized": "", "elastic_gpu_specifications": [], "elastic_inference_accelerator": [], "enclave_options": [], "hibernation_options": [], "iam_instance_profile": [{"arn": "", "name": "cb365-web-instance-profile"}], "id": "lt-072e7cda0719a3129", "image_id": "ami-01d981a9051cd1d58", "instance_initiated_shutdown_behavior": "", "instance_market_options": [], "instance_requirements": [], "instance_type": "t2.micro", "kernel_id": "", "key_name": "test-s", "latest_version": 1, "license_specification": [], "maintenance_options": [], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "", "http_put_response_hop_limit": 2, "http_tokens": "required", "instance_metadata_tags": ""}], "monitoring": [{"enabled": true}], "name": "bastion-template-20250326191642529600000007", "name_prefix": "bastion-template-", "network_interfaces": [{"associate_carrier_ip_address": "", "associate_public_ip_address": "true", "connection_tracking_specification": [], "delete_on_termination": "true", "description": "", "device_index": 0, "interface_type": "", "ipv4_address_count": 0, "ipv4_addresses": [], "ipv4_prefix_count": 0, "ipv4_prefixes": [], "ipv6_address_count": 0, "ipv6_addresses": [], "ipv6_prefix_count": 0, "ipv6_prefixes": [], "network_card_index": 0, "network_interface_id": "", "primary_ipv6": "", "private_ip_address": "", "security_groups": ["sg-0a3bd691557eb0d4b"], "subnet_id": "subnet-01a2fcf09d6258314"}], "placement": [], "private_dns_name_options": [], "ram_disk_id": "", "security_group_names": [], "tag_specifications": [], "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-bastion-instance", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-bastion-instance", "Project": "cb365-web-infrastructure"}, "update_default_version": null, "user_data": "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", "vpc_security_group_ids": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_autoscaling_group.app_asg", "aws_efs_file_system.shared", "aws_efs_mount_target.mount_target", "aws_eip.bastion_host_eip", "aws_iam_instance_profile.instance_profile", "aws_iam_role.instance_role", "aws_launch_template.app_template", "aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_subnet.app_and_db_subnet", "aws_subnet.bastion_host_subnet", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allocation_id": "eipalloc-0340cbaeeaa8ea8cf", "association_id": "eipassoc-0858ac0737503f415", "connectivity_type": "public", "id": "nat-0f085c6b3e89df24b", "network_interface_id": "eni-0d34e20e5ff85d583", "private_ip": "*********", "public_ip": "*************", "secondary_allocation_ids": null, "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-01a2fcf09d6258314", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-nat-gateway", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-nat-gateway", "Project": "cb365-web-infrastructure"}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route", "name": "private_nat_route", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "0.0.0.0/0", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-085c4cf6753b0df821080289494", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "nat-0f085c6b3e89df24b", "network_interface_id": "", "origin": "CreateRoute", "route_table_id": "rtb-085c4cf6753b0df82", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat", "aws_route_table.private_rt", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private_rt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:route-table/rtb-085c4cf6753b0df82", "id": "rtb-085c4cf6753b0df82", "owner_id": "************", "propagating_vgws": [], "route": [], "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-private-rt", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-private-rt", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public_rt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:route-table/rtb-0ca2daf1eb2dbd71e", "id": "rtb-0ca2daf1eb2dbd71e", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-0d362cb1bb52bd2c5", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-public-rt", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-public-rt", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.igw", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private_rta", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0f4f1bcbe8b2dcecb", "route_table_id": "rtb-085c4cf6753b0df82", "subnet_id": "subnet-07fe6a511ecaf983f", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.private_rt", "aws_subnet.app_and_db_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public_rta", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-02e85aba67f222c8d", "route_table_id": "rtb-0ca2daf1eb2dbd71e", "subnet_id": "subnet-01a2fcf09d6258314", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public_rt", "aws_subnet.bastion_host_subnet", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "app_db_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:security-group/sg-053ee0368af8facde", "description": "Security group for Application and Database Servers", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "All outbound traffic", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-053ee0368af8facde", "ingress": [{"cidr_blocks": [], "description": "Django app traffic from Nginx", "from_port": 8000, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0a3bd691557eb0d4b"], "self": false, "to_port": 8000}, {"cidr_blocks": [], "description": "MySQL access within security group", "from_port": 3306, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": true, "to_port": 3306}, {"cidr_blocks": [], "description": "SSH from Bastion host", "from_port": 22, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0a3bd691557eb0d4b"], "self": false, "to_port": 22}], "name": "app_db_sg", "name_prefix": "", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-app-db-sg", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-app-db-sg", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.bastion_sg", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_security_group", "name": "bastion_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:security-group/sg-0a3bd691557eb0d4b", "description": "Security group for Bastion Host and Nginx Load Balancer", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "All outbound traffic", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0a3bd691557eb0d4b", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "HTTP access from internet", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}, {"cidr_blocks": ["0.0.0.0/0"], "description": "HTTPS access from internet", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}, {"cidr_blocks": ["0.0.0.0/0"], "description": "SSH access from trusted IP", "from_port": 22, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 22}], "name": "bastion_sg", "name_prefix": "", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-bastion-host-sg", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-bastion-host-sg", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_security_group", "name": "efs_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:security-group/sg-088e4fea95b6e6752", "description": "Security group for EFS mount targets", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "All outbound traffic", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-088e4fea95b6e6752", "ingress": [{"cidr_blocks": [], "description": "NFS access from Bastion and App instances", "from_port": 2049, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-053ee0368af8facde", "sg-0a3bd691557eb0d4b"], "self": false, "to_port": 2049}], "name": "efs_sg", "name_prefix": "", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-efs-sg", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-efs-sg", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "app_db_nfs_egress", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": "NFS egress to EFS", "from_port": 2049, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-053ee0368af8facde", "security_group_rule_id": "sgr-00c05f51034a604f5", "self": false, "source_security_group_id": "sg-088e4fea95b6e6752", "timeouts": null, "to_port": 2049, "type": "egress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "bastion_nfs_egress", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": "NFS egress to EFS", "from_port": 2049, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0a3bd691557eb0d4b", "security_group_rule_id": "sgr-0597baa79f6f5dc26", "self": false, "source_security_group_id": "sg-088e4fea95b6e6752", "timeouts": null, "to_port": 2049, "type": "egress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.app_db_sg", "aws_security_group.bastion_sg", "aws_security_group.efs_sg", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_sns_topic", "name": "cloudwatch_alarms", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"application_failure_feedback_role_arn": "", "application_success_feedback_role_arn": "", "application_success_feedback_sample_rate": 0, "archive_policy": "", "arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic", "beginning_archive_time": "", "content_based_deduplication": false, "delivery_policy": "", "display_name": "", "fifo_topic": false, "firehose_failure_feedback_role_arn": "", "firehose_success_feedback_role_arn": "", "firehose_success_feedback_sample_rate": 0, "http_failure_feedback_role_arn": "", "http_success_feedback_role_arn": "", "http_success_feedback_sample_rate": 0, "id": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic", "kms_master_key_id": "", "lambda_failure_feedback_role_arn": "", "lambda_success_feedback_role_arn": "", "lambda_success_feedback_sample_rate": 0, "name": "cloudwatch-alarms-topic", "name_prefix": "", "owner": "************", "policy": "{\"Statement\":[{\"Action\":\"SNS:Publish\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"cloudwatch.amazonaws.com\"},\"Resource\":\"arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic\",\"Sid\":\"AllowCloudWatchAlarms\"}],\"Version\":\"2012-10-17\"}", "signature_version": 0, "sqs_failure_feedback_role_arn": "", "sqs_success_feedback_role_arn": "", "sqs_success_feedback_sample_rate": 0, "tags": {"Backup": "true", "ManagedBy": "Terraform", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Project": "cb365-web-infrastructure"}, "tracing_config": ""}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_sns_topic_policy", "name": "cloudwatch_alarms", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic", "id": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic", "owner": "************", "policy": "{\"Statement\":[{\"Action\":\"SNS:Publish\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"cloudwatch.amazonaws.com\"},\"Resource\":\"arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic\",\"Sid\":\"AllowCloudWatchAlarms\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_sns_topic.cloudwatch_alarms"]}]}, {"mode": "managed", "type": "aws_sns_topic_subscription", "name": "email", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:865c4f69-7091-4458-a732-db4929abe297", "confirmation_timeout_in_minutes": 1, "confirmation_was_authenticated": false, "delivery_policy": "", "endpoint": "<EMAIL>", "endpoint_auto_confirms": false, "filter_policy": "", "filter_policy_scope": "", "id": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:865c4f69-7091-4458-a732-db4929abe297", "owner_id": "************", "pending_confirmation": true, "protocol": "email", "raw_message_delivery": false, "redrive_policy": "", "replay_policy": "", "subscription_role_arn": "", "topic_arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_sns_topic.cloudwatch_alarms"]}, {"index_key": 1, "schema_version": 0, "attributes": {"arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:735d50bb-5bf0-46ae-91e5-7f93846c2ead", "confirmation_timeout_in_minutes": 1, "confirmation_was_authenticated": false, "delivery_policy": "", "endpoint": "<EMAIL>", "endpoint_auto_confirms": false, "filter_policy": "", "filter_policy_scope": "", "id": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:735d50bb-5bf0-46ae-91e5-7f93846c2ead", "owner_id": "************", "pending_confirmation": true, "protocol": "email", "raw_message_delivery": false, "redrive_policy": "", "replay_policy": "", "subscription_role_arn": "", "topic_arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_sns_topic.cloudwatch_alarms"]}]}, {"mode": "managed", "type": "aws_sns_topic_subscription", "name": "sms", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:8ab2979a-5d37-40d3-91ce-8b5fdcdcced3", "confirmation_timeout_in_minutes": 1, "confirmation_was_authenticated": true, "delivery_policy": "", "endpoint": "+610452387934", "endpoint_auto_confirms": false, "filter_policy": "", "filter_policy_scope": "", "id": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:8ab2979a-5d37-40d3-91ce-8b5fdcdcced3", "owner_id": "************", "pending_confirmation": false, "protocol": "sms", "raw_message_delivery": false, "redrive_policy": "", "replay_policy": "", "subscription_role_arn": "", "topic_arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_sns_topic.cloudwatch_alarms"]}, {"index_key": 1, "schema_version": 0, "attributes": {"arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:7c2e7627-85f6-4b5a-b02c-e4c554a14ace", "confirmation_timeout_in_minutes": 1, "confirmation_was_authenticated": true, "delivery_policy": "", "endpoint": "+2348064612018", "endpoint_auto_confirms": false, "filter_policy": "", "filter_policy_scope": "", "id": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic:7c2e7627-85f6-4b5a-b02c-e4c554a14ace", "owner_id": "************", "pending_confirmation": false, "protocol": "sms", "raw_message_delivery": false, "redrive_policy": "", "replay_policy": "", "subscription_role_arn": "", "topic_arn": "arn:aws:sns:eu-west-1:************:cloudwatch-alarms-topic"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_sns_topic.cloudwatch_alarms"]}]}, {"mode": "managed", "type": "aws_ssm_parameter", "name": "app_cw_agent_config", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:eu-west-1:************:parameter/AmazonCloudWatch-app/config", "data_type": "text", "description": "Optimized app server config", "id": "/AmazonCloudWatch-app/config", "insecure_value": null, "key_id": "", "name": "/AmazonCloudWatch-app/config", "overwrite": null, "tags": null, "tags_all": {}, "tier": "Standard", "type": "String", "value": "{\"agent\":{\"metrics_collection_interval\":60,\"run_as_user\":\"cwagent\"},\"metrics\":{\"metrics_collected\":{\"cpu\":{\"measurement\":[\"cpu_usage_user\"],\"metrics_collection_interval\":60},\"disk\":{\"measurement\":[\"disk_used_percent\"],\"metrics_collection_interval\":60,\"resources\":[\"/\",\"/dev/xvda1\"]},\"mem\":{\"measurement\":[\"mem_used_percent\"],\"metrics_collection_interval\":60}},\"namespace\":\"AppServers\"}}", "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}]], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ssm_parameter", "name": "bastion_cw_agent_config", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:eu-west-1:************:parameter/AmazonCloudWatch-bastion/config", "data_type": "text", "description": "Enhanced config for bastion+nginx hosts", "id": "/AmazonCloudWatch-bastion/config", "insecure_value": null, "key_id": "", "name": "/AmazonCloudWatch-bastion/config", "overwrite": null, "tags": null, "tags_all": {}, "tier": "Standard", "type": "String", "value": "{\"agent\":{\"debug\":false,\"metrics_collection_interval\":60,\"run_as_user\":\"cwagent\"},\"logs\":{\"logs_collected\":{\"files\":{\"collect_list\":[{\"file_path\":\"/var/log/nginx/access.log\",\"log_group_name\":\"/bastion/nginx/access\",\"retention_in_days\":7},{\"file_path\":\"/var/log/nginx/error.log\",\"log_group_name\":\"/bastion/nginx/error\",\"retention_in_days\":30}]}}},\"metrics\":{\"append_dimensions\":{\"AutoScalingGroupName\":\"${aws:AutoScalingGroupName}\",\"InstanceId\":\"${aws:InstanceId}\"},\"metrics_collected\":{\"cpu\":{\"measurement\":[\"cpu_usage_user\"],\"metrics_collection_interval\":60,\"totalcpu\":false},\"disk\":{\"measurement\":[\"disk_used_percent\"],\"metrics_collection_interval\":60,\"resources\":[\"/\",\"/dev/xvda1\"]},\"mem\":{\"measurement\":[\"mem_used_percent\"],\"metrics_collection_interval\":60},\"nginx\":{\"metrics_collection_interval\":60,\"nginx_status_url\":\"http://localhost/nginx_status\"}},\"namespace\":\"Bastion/Nginx\"}}", "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value"}]], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_subnet", "name": "app_and_db_subnet", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:subnet/subnet-07fe6a511ecaf983f", "assign_ipv6_address_on_creation": false, "availability_zone": "eu-west-1a", "availability_zone_id": "euw1-az1", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-07fe6a511ecaf983f", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-private-subnet", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-private-subnet", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_subnet", "name": "bastion_host_subnet", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:subnet/subnet-01a2fcf09d6258314", "assign_ipv6_address_on_creation": false, "availability_zone": "eu-west-1a", "availability_zone_id": "euw1-az1", "cidr_block": "10.0.0.0/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-01a2fcf09d6258314", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-bastion-host-subnet", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-bastion-host-subnet", "Project": "cb365-web-infrastructure"}, "timeouts": null, "vpc_id": "vpc-0601cbddbb123efed"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:eu-west-1:************:vpc/vpc-0601cbddbb123efed", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0cda47f2ffda9d006", "default_route_table_id": "rtb-0fec86eba1c71acf0", "default_security_group_id": "sg-0c7f5530d5f3e8960", "dhcp_options_id": "dopt-04159266e3939c50b", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0601cbddbb123efed", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0fec86eba1c71acf0", "owner_id": "************", "tags": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-vpc", "Project": "cb365-web-infrastructure"}, "tags_all": {"Backup": "true", "ManagedBy": "Terraform", "Name": "cb365-web-vpc", "Project": "cb365-web-infrastructure"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "create_before_destroy": true}]}], "check_results": null}