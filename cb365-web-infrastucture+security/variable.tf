# VPC Configuration
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  default     = "10.0.0.0/16"
}

variable "bastion_host_subnet_cidr" {
  description = "CIDR block for the bastion host instance (using existing subnet)"
  default     = "********/24"  # Updated to match existing cb365-prod-public-subnet-1
}

variable "app_and_db_subnet_cidr" {
  description = "CIDR block for the private subnet (using existing subnet)"
  default     = "*********/24"  # Updated to match existing cb365-prod-private-subnet-1
}

variable "bastion_host_instance_type" {
  description = "Instance type for the bastion host"
  default     = "t2.micro"
}

variable "app_and_db_instance_type" {
  description = "Instance type for the application and database"
  default     = "t2.small"
}

variable "bastion_host_key" {
  description = "SSH key name for EC2 access"
  type        = string
  #default     = "key"
  default = "test-s"
}

variable "cb365_terraform_s3_bucket" {
  description = "cb365 remote s3 bucket"
  type        = string
  default     = "cb365-terraform-s3-bucket"
}

variable "cb365_dynamodb" {
  description = "cb365 remote dynamodb"
  type        = string
  default     = "cb365_dynamodb"
}


variable "cb365_bastion_host_ami" {
  description = "cb365 bastion host ami"
  type        = string
  default     = "ami-01d981a9051cd1d58"
}

variable "cb365_private_instance_ami" {
  description = "cb365 app and server"
  type        = string
  default     = "ami-0dffca59de3f3529c"
}


variable "alert_email_addresses" {
  description = "List of email addresses for CloudWatch alarm notifications"
  type        = list(string)
}

variable "alert_phone_numbers" {
  description = "List of phone numbers for SMS notifications (E.164 format)"
  type        = list(string)
}

variable "aws_region" {
  description = "aws region"
  type        = string
  default     = "eu-west-1"
}