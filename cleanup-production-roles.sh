#!/bin/bash

# <PERSON><PERSON>t to safely delete IAM roles in production account
# Deletes AdminRole LAST to maintain access

set -e

PROFILE="cb365-prod"

echo "🧹 Cleaning up IAM roles in production account..."
echo "⚠️  AdminRole will be deleted LAST to maintain access"
echo ""

# Function to delete role safely
delete_role() {
    local role_name=$1
    echo "Deleting role: $role_name"
    
    # First detach all managed policies
    echo "  - Detaching managed policies..."
    aws iam list-attached-role-policies --role-name "$role_name" --profile "$PROFILE" \
        --query 'AttachedPolicies[].PolicyArn' --output text | \
    while read -r policy_arn; do
        if [ -n "$policy_arn" ]; then
            echo "    Detaching: $policy_arn"
            aws iam detach-role-policy --role-name "$role_name" --policy-arn "$policy_arn" --profile "$PROFILE"
        fi
    done
    
    # Then delete inline policies
    echo "  - Deleting inline policies..."
    aws iam list-role-policies --role-name "$role_name" --profile "$PROFILE" \
        --query 'PolicyNames' --output text | \
    while read -r policy_name; do
        if [ -n "$policy_name" ]; then
            echo "    Deleting: $policy_name"
            aws iam delete-role-policy --role-name "$role_name" --policy-name "$policy_name" --profile "$PROFILE"
        fi
    done
    
    # Finally delete the role
    echo "  - Deleting role..."
    aws iam delete-role --role-name "$role_name" --profile "$PROFILE"
    echo "  ✅ $role_name deleted successfully"
    echo ""
}

# Delete Console roles first (safest)
echo "🎯 Step 1: Deleting Console roles..."
delete_role "ConsoleSecurityAuditRole"
delete_role "ConsoleReadOnlyRole" 
delete_role "ConsoleDeveloperRole"
delete_role "ConsoleAdminRole"

# Delete other CLI roles
echo "🎯 Step 2: Deleting other CLI roles..."
delete_role "SecurityAuditRole"
delete_role "ReadOnlyRole"
delete_role "DeveloperRole"

# Delete AdminRole LAST
echo "🎯 Step 3: Deleting AdminRole (LAST)..."
echo "⚠️  WARNING: After this, you'll lose CLI access to this account!"
read -p "Press Enter to continue or Ctrl+C to abort..."
delete_role "AdminRole"

echo "🎉 All roles deleted successfully!"
echo "📝 Next step: Deploy the correct CloudFormation template"
