AWSTemplateFormatVersion: '2010-09-09' 
Description: 'Cross-Account IAM Roles for Development Account'

Parameters:
  SecurityAccountId:
    Type: String
    Default: '************'
    Description: 'Security account ID that will assume these roles'

Resources:
  # Admin Role
  AdminRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: AdminRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                'sts:ExternalId': 'development-AdminRole'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AdministratorAccess
      Tags:
        - Key: Purpose
          Value: CrossAccountAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: Environment
          Value: development

  # Developer Role
  DeveloperRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: DeveloperRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                'sts:ExternalId': 'development-DeveloperRole'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/PowerUserAccess
      Tags:
        - Key: Purpose
          Value: CrossAccountAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: Environment
          Value: development

  # ReadOnly Role
  ReadOnlyRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ReadOnlyRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                'sts:ExternalId': 'development-ReadOnlyRole'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/ReadOnlyAccess
      Tags:
        - Key: Purpose
          Value: CrossAccountAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: Environment
          Value: development

  # Security Audit Role
  SecurityAuditRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: SecurityAuditRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                'sts:ExternalId': 'development-SecurityAuditRole'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/SecurityAudit
      Tags:
        - Key: Purpose
          Value: CrossAccountAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: Environment
          Value: development

  # Console-Compatible Roles (No External IDs for AWS Console access)

  # Console Admin Role - For Administrators group members
  ConsoleAdminRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ConsoleAdminRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              Bool:
                'aws:MultiFactorAuthPresent': 'true'
              StringEquals:
                'aws:RequestedRegion': 'eu-west-1'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AdministratorAccess
      Tags:
        - Key: Purpose
          Value: ConsoleAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: AccessType
          Value: Console

  # Console Developer Role - For Developers group (full access in development)
  ConsoleDeveloperRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ConsoleDeveloperRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                'aws:RequestedRegion': 'eu-west-1'
              Bool:
                'aws:MultiFactorAuthPresent': 'true'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/PowerUserAccess
      Tags:
        - Key: Purpose
          Value: ConsoleAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: AccessType
          Value: Console

  # Console ReadOnly Role - For ReadOnlyUsers group
  ConsoleReadOnlyRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ConsoleReadOnlyRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                'aws:RequestedRegion': 'eu-west-1'
              Bool:
                'aws:MultiFactorAuthPresent': 'true'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/ReadOnlyAccess
      Tags:
        - Key: Purpose
          Value: ConsoleAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: AccessType
          Value: Console

  # Console Security Audit Role - For SecurityAuditors group
  ConsoleSecurityAuditRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ConsoleSecurityAuditRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root'
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                'aws:RequestedRegion': 'eu-west-1'
              Bool:
                'aws:MultiFactorAuthPresent': 'true'
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/SecurityAudit
      Tags:
        - Key: Purpose
          Value: ConsoleAccess
        - Key: SourceAccount
          Value: !Ref SecurityAccountId
        - Key: Project
          Value: CB365-IAM
        - Key: AccessType
          Value: Console

Outputs:
  # CLI Roles (with External IDs)
  AdminRoleArn:
    Description: 'ARN of the Admin Role (CLI access with External ID)'
    Value: !GetAtt AdminRole.Arn

  DeveloperRoleArn:
    Description: 'ARN of the Developer Role (CLI access with External ID)'
    Value: !GetAtt DeveloperRole.Arn

  ReadOnlyRoleArn:
    Description: 'ARN of the ReadOnly Role (CLI access with External ID)'
    Value: !GetAtt ReadOnlyRole.Arn

  SecurityAuditRoleArn:
    Description: 'ARN of the Security Audit Role (CLI access with External ID)'
    Value: !GetAtt SecurityAuditRole.Arn

  # Console Roles (no External IDs)
  ConsoleAdminRoleArn:
    Description: 'ARN of the Console Admin Role (Console access, no External ID)'
    Value: !GetAtt ConsoleAdminRole.Arn

  ConsoleDeveloperRoleArn:
    Description: 'ARN of the Console Developer Role (Console access, no External ID)'
    Value: !GetAtt ConsoleDeveloperRole.Arn

  ConsoleReadOnlyRoleArn:
    Description: 'ARN of the Console ReadOnly Role (Console access, no External ID)'
    Value: !GetAtt ConsoleReadOnlyRole.Arn

  ConsoleSecurityAuditRoleArn:
    Description: 'ARN of the Console Security Audit Role (Console access, no External ID)'
    Value: !GetAtt ConsoleSecurityAuditRole.Arn
