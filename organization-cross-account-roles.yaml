AWSTemplateFormatVersion: '2010-09-09' 
Description: 'Cross-Account IAM Roles for Organization Account' 
 
Parameters: 
  SecurityAccountId: 
    Type: String 
    Default: '************' 
    Description: 'Security account ID that will assume these roles' 
 
Resources: 
  # Organization Admin Role (for organization management)
  OrganizationAdminRole: 
    Type: AWS::IAM::Role 
    Properties: 
      RoleName: OrganizationAdminRole 
      AssumeRolePolicyDocument: 
        Version: '2012-10-17' 
        Statement: 
          - Effect: Allow 
            Principal: 
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root' 
            Action: sts:AssumeRole 
            Condition: 
              StringEquals: 
                'sts:ExternalId': 'organization-OrganizationAdminRole' 
      ManagedPolicyArns: 
        - arn:aws:iam::aws:policy/AdministratorAccess 
      Tags: 
        - Key: Purpose 
          Value: CrossAccountAccess 
        - Key: SourceAccount 
          Value: !Ref SecurityAccountId 
        - Key: Project 
          Value: CB365-IAM 
        - Key: Environment
          Value: organization
 

 
  # Console Organization Admin Role - For Administrators group members 
  ConsoleOrganizationAdminRole: 
    Type: AWS::IAM::Role 
    Properties: 
      RoleName: ConsoleOrganizationAdminRole 
      AssumeRolePolicyDocument: 
        Version: '2012-10-17' 
        Statement: 
          - Effect: Allow 
            Principal: 
              AWS: !Sub 'arn:aws:iam::${SecurityAccountId}:root' 
            Action: sts:AssumeRole 
            Condition: 
              Bool: 
                'aws:MultiFactorAuthPresent': 'true' 
              StringEquals: 
                'aws:RequestedRegion': 'eu-west-1' 
      ManagedPolicyArns: 
        - arn:aws:iam::aws:policy/AdministratorAccess 
      Tags: 
        - Key: Purpose 
          Value: ConsoleAccess 
        - Key: SourceAccount 
          Value: !Ref SecurityAccountId 
        - Key: Project 
          Value: CB365-IAM 
        - Key: AccessType 
          Value: Console
        - Key: Environment
          Value: organization
 

 
Outputs: 
  # CLI Roles (with External IDs) 
  OrganizationAdminRoleArn: 
    Description: 'ARN of the Organization Admin Role (CLI access with External ID)' 
    Value: !GetAtt OrganizationAdminRole.Arn 
 
  # Console Roles (no External IDs)
  ConsoleOrganizationAdminRoleArn:
    Description: 'ARN of the Console Organization Admin Role (Console access, no External ID)'
    Value: !GetAtt ConsoleOrganizationAdminRole.Arn
